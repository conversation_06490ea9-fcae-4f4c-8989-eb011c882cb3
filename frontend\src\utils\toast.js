import { toast as toastify } from 'react-toastify';

export const toast = {
  success: (message) => {
    toastify.success(message);
  },
  error: (message) => {
    toastify.error(message);
  },
  info: (message) => {
    toastify.info(message);
  },
  warning: (message) => {
    toastify.warning(message);
  },
  loading: (message) => {
    return toastify.loading(message);
  },
  dismiss: (toastId) => {
    toastify.dismiss(toastId);
  }
};

export default toast;
