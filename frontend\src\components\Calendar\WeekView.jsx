import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { motion } from "framer-motion";
import { formatDate, formatTime, isToday } from "../../utils/dateUtils";
import { EVENT_CATEGORIES } from "../../utils/constants";

const WeekContainer = styled.div`
  padding: ${({ theme }) => theme.spacing[4]};
  overflow-x: auto;

  /* Desktop web view - spacious week container */
  @media (min-width: 769px) {
    padding: ${({ theme }) => theme.spacing[6]};
    overflow-x: visible;
  }

  /* Mobile native feel - completely different layout */
  @media (max-width: 768px) {
    padding: 0;
    overflow-x: visible;
    display: flex;
    flex-direction: column;
  }
`;

const WeekGrid = styled.div`
  display: grid;
  grid-template-columns: 80px repeat(7, 1fr);
  gap: 1px;
  background: ${({ theme }) => theme.colors.border.light};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  overflow: hidden;
  min-width: 800px;

  /* Desktop web view - larger grid with better spacing */
  @media (min-width: 769px) {
    grid-template-columns: 100px repeat(7, 1fr);
    min-width: 900px;
    border-radius: ${({ theme }) => theme.borderRadius.lg};
    box-shadow: ${({ theme }) => theme.shadows.sm};
  }

  /* Mobile native feel - hide the complex grid, use mobile layout instead */
  @media (max-width: 768px) {
    display: none;
  }
`;

const TimeSlot = styled.div`
  background: ${({ theme }) => theme.colors.background.secondary};
  padding: ${({ theme }) => theme.spacing[2]};
  text-align: center;
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.text.secondary};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  display: flex;
  align-items: center;
  justify-content: center;

  border-right: 1px solid ${({ theme }) => theme.colors.border.light};
  min-height: 60px;

  /* Desktop web view - larger time slots */
  @media (min-width: 769px) {
    padding: ${({ theme }) => theme.spacing[3]};
    font-size: ${({ theme }) => theme.fontSizes.sm};
    min-height: 70px;
  }

  /* Mobile native feel - compact time slots */
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing[1]};
    font-size: 10px;
    min-height: 50px;
  }
`;

const DayHeader = styled.div`
  background: ${({ theme, isToday }) =>
    isToday ? theme.colors.primary[100] : theme.colors.background.secondary};
  padding: ${({ theme }) => theme.spacing[3]};
  text-align: center;
  border-bottom: ${({ theme, isToday }) =>
    isToday ? `2px solid ${theme.colors.primary[500]}` : "none"};

  /* Desktop web view - larger day headers */
  @media (min-width: 769px) {
    padding: ${({ theme }) => theme.spacing[4]};
  }

  /* Mobile native feel - compact day headers */
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing[2]};
  }
`;

const DayName = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme, isToday }) =>
    isToday ? theme.colors.primary[700] : theme.colors.text.secondary};
  margin-bottom: ${({ theme }) => theme.spacing[1]};

  /* Desktop web view - larger day names */
  @media (min-width: 769px) {
    font-size: ${({ theme }) => theme.fontSizes.base};
  }

  /* Mobile native feel - compact day names */
  @media (max-width: 768px) {
    font-size: ${({ theme }) => theme.fontSizes.xs};
    margin-bottom: 2px;
  }
`;

const DayNumber = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme, isToday }) =>
    isToday ? theme.colors.primary[700] : theme.colors.text.primary};

  /* Desktop web view - larger day numbers */
  @media (min-width: 769px) {
    font-size: ${({ theme }) => theme.fontSizes.xl};
  }

  /* Mobile native feel - compact day numbers */
  @media (max-width: 768px) {
    font-size: ${({ theme }) => theme.fontSizes.base};
  }
`;

const DayColumn = styled.div`
  background: ${({ theme }) => theme.colors.background.primary};
  min-height: 600px;
  position: relative;
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};

  &:hover {
    background: ${({ theme }) => theme.colors.primary[25]};
  }

  /* Desktop web view - larger day columns with hover effects */
  @media (min-width: 769px) {
    min-height: 700px;

    &:hover {
      background: ${({ theme }) => theme.colors.primary[50]};
      transform: translateY(-1px);
    }
  }

  /* Mobile native feel - compact day columns with touch optimization */
  @media (max-width: 768px) {
    min-height: 400px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      transform: none; /* Disable hover effects on mobile */
    }

    &:active {
      background: ${({ theme }) => theme.colors.primary[100]};
    }
  }
`;

const EventBlock = styled(motion.div)`
  position: absolute;
  left: 4px;
  right: 4px;
  background: ${({ theme, category }) => {
    const categoryData = EVENT_CATEGORIES.find((cat) => cat.value === category);
    return categoryData ? theme.colors.primary[500] : theme.colors.gray[500];
  }};
  color: white;
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  cursor: pointer;
  overflow: hidden;
  z-index: 1;

  &:hover {
    transform: translateY(-1px);
    box-shadow: ${({ theme }) => theme.shadows.md};
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    left: 2px;
    right: 2px;
    padding: ${({ theme }) => theme.spacing[1]};
    font-size: 10px;
  }
`;

const EventTitle = styled.div`
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  margin-bottom: ${({ theme }) => theme.spacing[1]};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const EventTime = styled.div`
  opacity: 0.9;
  font-size: ${({ theme }) => theme.fontSizes.xs};
`;

// Mobile-specific week layout components
const MobileWeekHeader = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: flex;
    background: ${({ theme }) => theme.colors.background.secondary};
    padding: ${({ theme }) => theme.spacing[2]};
    border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }
`;

const MobileDayTab = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;
    padding: ${({ theme }) => theme.spacing[2]};
    cursor: pointer;
    border-radius: ${({ theme }) => theme.borderRadius.md};
    background: ${({ isSelected, theme }) =>
      isSelected ? theme.colors.primary[100] : "transparent"};
    border: ${({ isSelected, theme }) =>
      isSelected
        ? `2px solid ${theme.colors.primary[500]}`
        : "2px solid transparent"};
    transition: all ${({ theme }) => theme.transitions.fast};
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;

    &:active {
      transform: scale(0.95);
    }
  }
`;

const MobileDayName = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: block;
    font-size: ${({ theme }) => theme.fontSizes.xs};
    font-weight: ${({ theme }) => theme.fontWeights.medium};
    color: ${({ theme, isToday, isSelected }) => {
      if (isSelected) return theme.colors.primary[700];
      if (isToday) return theme.colors.primary[600];
      return theme.colors.text.secondary;
    }};
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 2px;
  }
`;

const MobileDayNumber = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: block;
    font-size: ${({ theme }) => theme.fontSizes.lg};
    font-weight: ${({ theme }) => theme.fontWeights.bold};
    color: ${({ theme, isToday, isSelected }) => {
      if (isSelected) return theme.colors.primary[700];
      if (isToday) return theme.colors.primary[600];
      return theme.colors.text.primary;
    }};
    line-height: 1;
  }
`;

const MobileEventsList = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: flex;
    flex-direction: column;
    padding: ${({ theme }) => theme.spacing[3]};
    gap: ${({ theme }) => theme.spacing[2]};
    max-height: 400px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
`;

const MobileEventCard = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: flex;
    flex-direction: column;
    background: ${({ theme }) => theme.colors.background.primary};
    border: 1px solid ${({ theme }) => theme.colors.border.light};
    border-radius: ${({ theme }) => theme.borderRadius.lg};
    padding: ${({ theme }) => theme.spacing[3]};
    box-shadow: ${({ theme }) => theme.shadows.sm};
    cursor: pointer;
    transition: all ${({ theme }) => theme.transitions.fast};
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;

    &:active {
      transform: scale(0.98);
      box-shadow: ${({ theme }) => theme.shadows.md};
    }
  }
`;

const MobileEventTime = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: block;
    font-size: ${({ theme }) => theme.fontSizes.sm};
    font-weight: ${({ theme }) => theme.fontWeights.semibold};
    color: ${({ theme }) => theme.colors.primary[600]};
    margin-bottom: ${({ theme }) => theme.spacing[1]};
  }
`;

const MobileEventTitle = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: block;
    font-size: ${({ theme }) => theme.fontSizes.base};
    font-weight: ${({ theme }) => theme.fontWeights.medium};
    color: ${({ theme }) => theme.colors.text.primary};
    margin-bottom: ${({ theme }) => theme.spacing[1]};
    line-height: 1.3;
  }
`;

const MobileNoEvents = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: ${({ theme }) => theme.spacing[6]};
    color: ${({ theme }) => theme.colors.text.secondary};
    text-align: center;
  }
`;

const WeekView = ({ currentDate, events, onEventClick, onDateClick }) => {
  const [selectedMobileDay, setSelectedMobileDay] = useState(currentDate);

  const weekStart = new Date(currentDate);
  weekStart.setDate(currentDate.getDate() - currentDate.getDay());

  const weekDays = [];
  for (let i = 0; i < 7; i++) {
    const day = new Date(weekStart);
    day.setDate(weekStart.getDate() + i);
    weekDays.push(day);
  }

  // Set initial selected day to today or current date
  useEffect(() => {
    setSelectedMobileDay(currentDate);
  }, [currentDate]);

  const timeSlots = [];
  for (let hour = 0; hour < 24; hour++) {
    timeSlots.push({
      hour,
      label:
        hour === 0
          ? "12 AM"
          : hour < 12
          ? `${hour} AM`
          : hour === 12
          ? "12 PM"
          : `${hour - 12} PM`,
    });
  }

  const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  // Group events by date
  const eventsByDate = events.reduce((acc, event) => {
    const eventDate = formatDate(event.startDate, "yyyy-MM-dd");
    if (!acc[eventDate]) {
      acc[eventDate] = [];
    }
    acc[eventDate].push(event);
    return acc;
  }, {});

  const getEventPosition = (event) => {
    const [startHour, startMinute] = event.startTime.split(":").map(Number);
    const [endHour, endMinute] = event.endTime.split(":").map(Number);

    const startPosition = (startHour + startMinute / 60) * 25; // 25px per hour
    const duration = endHour + endMinute / 60 - (startHour + startMinute / 60);
    const height = Math.max(duration * 25, 20); // Minimum 20px height

    return {
      top: `${startPosition}px`,
      height: `${height}px`,
    };
  };

  const handleDayClick = (date) => {
    if (onDateClick) {
      onDateClick(date);
    }
  };

  const handleEventClick = (event, e) => {
    e.stopPropagation();
    if (onEventClick) {
      onEventClick(event);
    }
  };

  const handleMobileDaySelect = (day) => {
    setSelectedMobileDay(day);
    if (onDateClick) {
      onDateClick(day);
    }
  };

  // Get events for the selected mobile day
  const selectedDayEvents = events
    .filter((event) => {
      const eventDate = formatDate(event.startDate, "yyyy-MM-dd");
      const selectedDate = formatDate(selectedMobileDay, "yyyy-MM-dd");
      return eventDate === selectedDate;
    })
    .sort((a, b) => {
      const timeA = a.startTime || "00:00";
      const timeB = b.startTime || "00:00";
      return timeA.localeCompare(timeB);
    });

  return (
    <WeekContainer>
      {/* Mobile Week Header - Day Tabs */}
      <MobileWeekHeader>
        {weekDays.map((day, index) => {
          const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
          const isSelected =
            formatDate(day, "yyyy-MM-dd") ===
            formatDate(selectedMobileDay, "yyyy-MM-dd");
          const isDayToday = isToday(day);

          return (
            <MobileDayTab
              key={day.toISOString()}
              isSelected={isSelected}
              onClick={() => handleMobileDaySelect(day)}
            >
              <MobileDayName isToday={isDayToday} isSelected={isSelected}>
                {dayNames[index]}
              </MobileDayName>
              <MobileDayNumber isToday={isDayToday} isSelected={isSelected}>
                {day.getDate()}
              </MobileDayNumber>
            </MobileDayTab>
          );
        })}
      </MobileWeekHeader>

      {/* Mobile Events List for Selected Day */}
      <MobileEventsList>
        {selectedDayEvents.length > 0 ? (
          selectedDayEvents.map((event) => (
            <MobileEventCard
              key={event._id}
              onClick={(e) => handleEventClick(event, e)}
            >
              <MobileEventTime>
                {formatTime(event.startTime)} - {formatTime(event.endTime)}
              </MobileEventTime>
              <MobileEventTitle>{event.title}</MobileEventTitle>
              {event.location && (
                <div
                  style={{
                    fontSize: "14px",
                    color: "#666",
                    marginTop: "4px",
                    display: "flex",
                    alignItems: "center",
                    gap: "4px",
                  }}
                >
                  📍{" "}
                  {typeof event.location === "string"
                    ? event.location
                    : event.location.name ||
                      `${event.location.address || ""}${
                        event.location.room ? ` - ${event.location.room}` : ""
                      }`.trim()}
                </div>
              )}
            </MobileEventCard>
          ))
        ) : (
          <MobileNoEvents>
            <div style={{ fontSize: "48px", marginBottom: "16px" }}>📅</div>
            <div
              style={{
                fontSize: "16px",
                fontWeight: "500",
                marginBottom: "8px",
              }}
            >
              No events scheduled
            </div>
            <div style={{ fontSize: "14px" }}>
              for {formatDate(selectedMobileDay, "EEEE, MMMM dd")}
            </div>
          </MobileNoEvents>
        )}
      </MobileEventsList>

      {/* Desktop Week Grid */}
      <WeekGrid>
        {/* Time column header */}
        <TimeSlot />

        {/* Day headers */}
        {weekDays.map((day, index) => (
          <DayHeader key={day.toISOString()} isToday={isToday(day)}>
            <DayName isToday={isToday(day)}>{dayNames[index]}</DayName>
            <DayNumber isToday={isToday(day)}>{day.getDate()}</DayNumber>
          </DayHeader>
        ))}

        {/* Time slots and day columns */}
        {timeSlots.map((timeSlot) => (
          <React.Fragment key={timeSlot.hour}>
            <TimeSlot>{timeSlot.label}</TimeSlot>

            {weekDays.map((day) => {
              const dateKey = formatDate(day, "yyyy-MM-dd");
              const dayEvents = eventsByDate[dateKey] || [];

              return (
                <DayColumn
                  key={`${day.toISOString()}-${timeSlot.hour}`}
                  onClick={() => handleDayClick(day)}
                >
                  {timeSlot.hour === 0 &&
                    dayEvents.map((event) => {
                      const position = getEventPosition(event);

                      return (
                        <EventBlock
                          key={event._id}
                          category={event.category}
                          style={position}
                          onClick={(e) => handleEventClick(event, e)}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          title={`${event.title} - ${formatTime(
                            event.startTime
                          )} to ${formatTime(event.endTime)}`}
                        >
                          <EventTitle>{event.title}</EventTitle>
                          <EventTime>
                            {formatTime(event.startTime)} -{" "}
                            {formatTime(event.endTime)}
                          </EventTime>
                        </EventBlock>
                      );
                    })}
                </DayColumn>
              );
            })}
          </React.Fragment>
        ))}
      </WeekGrid>
    </WeekContainer>
  );
};

export default WeekView;
