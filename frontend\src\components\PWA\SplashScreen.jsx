import React, { useEffect, useState } from 'react';
import styled, { keyframes } from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { Calendar, Heart, Users } from 'lucide-react';
import { useStandalone } from '../../hooks/useTouch';

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const pulse = keyframes`
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
`;

const SplashContainer = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    ${({ theme }) => theme.colors.primary[500]} 0%,
    ${({ theme }) => theme.colors.primary[600]} 50%,
    ${({ theme }) => theme.colors.primary[700]} 100%
  );
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: ${({ theme }) => theme.zIndex.modal + 100};
  color: white;
  padding: ${({ theme }) => theme.spacing[8]};
  text-align: center;
  
  /* Adjust for safe areas on mobile */
  padding-top: ${({ isStandalone }) => 
    isStandalone ? 'calc(env(safe-area-inset-top) + 2rem)' : '2rem'};
  padding-bottom: ${({ isStandalone }) => 
    isStandalone ? 'calc(env(safe-area-inset-bottom) + 2rem)' : '2rem'};
`;

const LogoContainer = styled(motion.div)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  animation: ${pulse} 2s ease-in-out infinite;
`;

const AppTitle = styled(motion.h1)`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  margin: 0 0 ${({ theme }) => theme.spacing[2]} 0;
  animation: ${fadeIn} 1s ease-out 0.5s both;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    font-size: ${({ theme }) => theme.fontSizes['2xl']};
  }
`;

const AppSubtitle = styled(motion.p)`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.normal};
  margin: 0 0 ${({ theme }) => theme.spacing[8]} 0;
  opacity: 0.9;
  animation: ${fadeIn} 1s ease-out 0.7s both;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    font-size: ${({ theme }) => theme.fontSizes.base};
  }
`;

const FeatureList = styled(motion.div)`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};
  animation: ${fadeIn} 1s ease-out 1s both;
`;

const FeatureItem = styled(motion.div)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  font-size: ${({ theme }) => theme.fontSizes.base};
  opacity: 0.9;
`;

const FeatureIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
`;

const LoadingContainer = styled(motion.div)`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[4]};
  animation: ${fadeIn} 1s ease-out 1.2s both;
`;

const LoadingBar = styled.div`
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: ${({ theme }) => theme.borderRadius.full};
  overflow: hidden;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    width: 150px;
  }
`;

const LoadingProgress = styled(motion.div)`
  height: 100%;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 1) 50%,
    rgba(255, 255, 255, 0.8) 100%
  );
  border-radius: ${({ theme }) => theme.borderRadius.full};
`;

const LoadingText = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  opacity: 0.8;
  margin: 0;
`;

const SplashScreen = ({ isVisible, onComplete, loadingProgress = 0 }) => {
  const isStandalone = useStandalone();
  const [currentFeature, setCurrentFeature] = useState(0);

  const features = [
    {
      icon: Calendar,
      text: "View church events and calendar"
    },
    {
      icon: Users,
      text: "Connect with your church community"
    },
    {
      icon: Heart,
      text: "Stay updated with church activities"
    }
  ];

  useEffect(() => {
    if (!isVisible) return;

    const featureInterval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 2000);

    return () => clearInterval(featureInterval);
  }, [isVisible, features.length]);

  useEffect(() => {
    if (loadingProgress >= 100) {
      const timer = setTimeout(() => {
        if (onComplete) onComplete();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [loadingProgress, onComplete]);

  return (
    <AnimatePresence>
      {isVisible && (
        <SplashContainer
          isStandalone={isStandalone}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          <LogoContainer
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ 
              type: "spring", 
              stiffness: 200, 
              damping: 20,
              delay: 0.2 
            }}
          >
            <Calendar size={48} />
          </LogoContainer>

          <AppTitle
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.6 }}
          >
            Grace Bible Fellowship
          </AppTitle>

          <AppSubtitle
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7, duration: 0.6 }}
          >
            Stay Connected with Your Church
          </AppSubtitle>

          <FeatureList
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1, duration: 0.6 }}
          >
            <AnimatePresence mode="wait">
              <FeatureItem
                key={currentFeature}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.4 }}
              >
                <FeatureIcon>
                  {React.createElement(features[currentFeature].icon, { size: 20 })}
                </FeatureIcon>
                {features[currentFeature].text}
              </FeatureItem>
            </AnimatePresence>
          </FeatureList>

          <LoadingContainer
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.2, duration: 0.6 }}
          >
            <LoadingBar>
              <LoadingProgress
                initial={{ width: "0%" }}
                animate={{ width: `${Math.min(loadingProgress, 100)}%` }}
                transition={{ duration: 0.3 }}
              />
            </LoadingBar>
            <LoadingText>
              {loadingProgress < 100 ? 'Loading your church community...' : 'Welcome!'}
            </LoadingText>
          </LoadingContainer>
        </SplashContainer>
      )}
    </AnimatePresence>
  );
};

export default SplashScreen;
