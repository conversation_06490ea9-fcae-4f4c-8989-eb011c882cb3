const mongoose = require("mongoose");

const activityLogSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: function () {
        return this.action !== "view_public";
      },
    },
    action: {
      type: String,
      required: [true, "Action is required"],
      enum: [
        "login",
        "logout",
        "create_event",
        "update_event",
        "delete_event",
        "view_event",
        "export_events",
        "upload_files",
        "delete_file",
        "create_user",
        "update_user",
        "delete_user",
        "view_public",
        "search_events",
      ],
    },
    resource: {
      type: String,
      enum: ["event", "user", "system"],
      required: [true, "Resource type is required"],
    },
    resourceId: {
      type: mongoose.Schema.Types.ObjectId,
      required: function () {
        return [
          "create_event",
          "update_event",
          "delete_event",
          "view_event",
          "create_user",
          "update_user",
          "delete_user",
        ].includes(this.action);
      },
    },
    details: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    ipAddress: {
      type: String,
      trim: true,
    },
    userAgent: {
      type: String,
      trim: true,
    },
    success: {
      type: Boolean,
      default: true,
    },
    errorMessage: {
      type: String,
      trim: true,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for better query performance
activityLogSchema.index({ user: 1, createdAt: -1 });
activityLogSchema.index({ action: 1, createdAt: -1 });
activityLogSchema.index({ resource: 1, resourceId: 1 });
activityLogSchema.index({ createdAt: -1 });

// TTL index to automatically delete old logs after 90 days
activityLogSchema.index({ createdAt: 1 }, { expireAfterSeconds: 7776000 }); // 90 days

// Static method to log activity
activityLogSchema.statics.logActivity = function (activityData) {
  return this.create(activityData);
};

// Static method to get user activity
activityLogSchema.statics.getUserActivity = function (userId, limit = 50) {
  return this.find({ user: userId })
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate("user", "username email role");
};

// Static method to get recent activities
activityLogSchema.statics.getRecentActivities = function (limit = 100) {
  return this.find({})
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate("user", "username email role");
};

module.exports = mongoose.model("ActivityLog", activityLogSchema);
