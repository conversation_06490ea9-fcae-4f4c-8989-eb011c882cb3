import React from "react";
import styled from "styled-components";
import { motion } from "framer-motion";
import { formatDate, formatTime, isToday } from "../../utils/dateUtils";
import { EVENT_CATEGORIES } from "../../utils/constants";

const DayContainer = styled.div`
  padding: ${({ theme }) => theme.spacing[4]};

  /* Mobile native feel - remove redundant header and optimize layout */
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing[2]};
  }
`;

const DayHeader = styled.div`
  background: ${({ theme, isToday }) =>
    isToday ? theme.colors.primary[100] : theme.colors.background.secondary};
  padding: ${({ theme }) => theme.spacing[4]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  text-align: center;
  border: ${({ theme, isToday }) =>
    isToday
      ? `2px solid ${theme.colors.primary[500]}`
      : `1px solid ${theme.colors.border.light}`};

  /* Mobile native feel - hide redundant header completely */
  @media (max-width: 768px) {
    display: none;
  }
`;

const DayTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes["2xl"]};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme, isToday }) =>
    isToday ? theme.colors.primary[700] : theme.colors.text.primary};
  margin: 0;
`;

const DayGrid = styled.div`
  display: grid;
  grid-template-columns: 100px 1fr;
  gap: 1px;
  background: ${({ theme }) => theme.colors.border.light};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  overflow: hidden;

  @media (max-width: 768px) {
    display: none; /* Hide complex grid on mobile */
  }
`;

/* Mobile-specific components for better day view */
const MobileDayEvents = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: flex;
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing[3]};
    padding: ${({ theme }) => theme.spacing[3]};
  }
`;

const MobileEventCard = styled(motion.div)`
  display: none;

  @media (max-width: 768px) {
    display: flex;
    flex-direction: column;
    background: ${({ theme }) => theme.colors.background.primary};
    border: 1px solid ${({ theme }) => theme.colors.border.light};
    border-radius: ${({ theme }) => theme.borderRadius.lg};
    padding: ${({ theme }) => theme.spacing[4]};
    box-shadow: ${({ theme }) => theme.shadows.sm};
    cursor: pointer;
    transition: all ${({ theme }) => theme.transitions.fast};
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;

    &:active {
      transform: scale(0.98);
      box-shadow: ${({ theme }) => theme.shadows.md};
    }
  }
`;

const MobileEventTime = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: block;
    font-size: ${({ theme }) => theme.fontSizes.lg};
    font-weight: ${({ theme }) => theme.fontWeights.bold};
    color: ${({ theme }) => theme.colors.primary[600]};
    margin-bottom: ${({ theme }) => theme.spacing[2]};
  }
`;

const MobileEventTitle = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: block;
    font-size: ${({ theme }) => theme.fontSizes.xl};
    font-weight: ${({ theme }) => theme.fontWeights.semibold};
    color: ${({ theme }) => theme.colors.text.primary};
    margin-bottom: ${({ theme }) => theme.spacing[2]};
    line-height: 1.3;
  }
`;

const MobileEventLocation = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: flex;
    align-items: center;
    gap: ${({ theme }) => theme.spacing[1]};
    font-size: ${({ theme }) => theme.fontSizes.base};
    color: ${({ theme }) => theme.colors.text.secondary};
    margin-bottom: ${({ theme }) => theme.spacing[2]};
  }
`;

const MobileEventDescription = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: block;
    font-size: ${({ theme }) => theme.fontSizes.base};
    color: ${({ theme }) => theme.colors.text.secondary};
    line-height: 1.4;
  }
`;

const MobileNoEvents = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: ${({ theme }) => theme.spacing[8]};
    text-align: center;
    color: ${({ theme }) => theme.colors.text.secondary};
  }
`;

const TimeSlot = styled.div`
  background: ${({ theme }) => theme.colors.background.secondary};
  padding: ${({ theme }) => theme.spacing[3]};
  text-align: center;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.secondary};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60px;

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    padding: ${({ theme }) => theme.spacing[2]};
    font-size: ${({ theme }) => theme.fontSizes.xs};
    min-height: 50px;
  }
`;

const HourSlot = styled.div`
  background: ${({ theme }) => theme.colors.background.primary};
  padding: ${({ theme }) => theme.spacing[2]};
  min-height: 60px;
  position: relative;
  cursor: pointer;
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background: ${({ theme }) => theme.colors.primary[25]};
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    min-height: 50px;
    padding: ${({ theme }) => theme.spacing[1]};
  }
`;

const EventBlock = styled(motion.div)`
  background: ${({ theme, category }) => {
    const categoryData = EVENT_CATEGORIES.find((cat) => cat.value === category);
    return categoryData ? theme.colors.primary[500] : theme.colors.gray[500];
  }};
  color: white;
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    padding: ${({ theme }) => theme.spacing[2]};
  }
`;

const EventTitle = styled.div`
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-bottom: ${({ theme }) => theme.spacing[1]};

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    font-size: ${({ theme }) => theme.fontSizes.xs};
  }
`;

const EventDetails = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  opacity: 0.9;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[1]};
`;

const EventTime = styled.div`
  font-weight: ${({ theme }) => theme.fontWeights.medium};
`;

const EventLocation = styled.div`
  opacity: 0.8;
`;

const NoEventsMessage = styled.div`
  text-align: center;
  color: ${({ theme }) => theme.colors.text.secondary};
  font-style: italic;
  padding: ${({ theme }) => theme.spacing[8]};
  background: ${({ theme }) => theme.colors.background.secondary};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  margin: ${({ theme }) => theme.spacing[4]} 0;
`;

const DayView = ({ currentDate, events, onEventClick, onDateClick }) => {
  const timeSlots = [];
  for (let hour = 0; hour < 24; hour++) {
    timeSlots.push({
      hour,
      label:
        hour === 0
          ? "12 AM"
          : hour < 12
          ? `${hour} AM`
          : hour === 12
          ? "12 PM"
          : `${hour - 12} PM`,
    });
  }

  // Filter events for the current day
  const dayEvents = events.filter((event) => {
    const eventDate = formatDate(event.startDate, "yyyy-MM-dd");
    const currentDateStr = formatDate(currentDate, "yyyy-MM-dd");
    return eventDate === currentDateStr;
  });

  // Group events by hour
  const eventsByHour = dayEvents.reduce((acc, event) => {
    const [hour] = event.startTime.split(":").map(Number);
    if (!acc[hour]) {
      acc[hour] = [];
    }
    acc[hour].push(event);
    return acc;
  }, {});

  const handleHourClick = (hour) => {
    const clickedDate = new Date(currentDate);
    clickedDate.setHours(hour, 0, 0, 0);
    if (onDateClick) {
      onDateClick(clickedDate);
    }
  };

  const handleEventClick = (event, e) => {
    e.stopPropagation();
    if (onEventClick) {
      onEventClick(event);
    }
  };

  const dayTitle = formatDate(currentDate, "EEEE, MMMM dd, yyyy");
  const isDayToday = isToday(currentDate);

  return (
    <DayContainer>
      <DayHeader isToday={isDayToday}>
        <DayTitle isToday={isDayToday}>{dayTitle}</DayTitle>
      </DayHeader>

      {/* Mobile Day Events - Clean List Layout */}
      <MobileDayEvents>
        {dayEvents.length > 0 ? (
          dayEvents
            .sort((a, b) => {
              const timeA = a.startTime || "00:00";
              const timeB = b.startTime || "00:00";
              return timeA.localeCompare(timeB);
            })
            .map((event) => (
              <MobileEventCard
                key={event._id}
                onClick={(e) => handleEventClick(event, e)}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MobileEventTime>
                  {formatTime(event.startTime)} - {formatTime(event.endTime)}
                </MobileEventTime>
                <MobileEventTitle>{event.title}</MobileEventTitle>
                {event.location && (
                  <MobileEventLocation>
                    📍{" "}
                    {typeof event.location === "string"
                      ? event.location
                      : event.location.name ||
                        `${event.location.address || ""}${
                          event.location.room ? ` - ${event.location.room}` : ""
                        }`.trim()}
                  </MobileEventLocation>
                )}
                {event.description && (
                  <MobileEventDescription>
                    {event.description}
                  </MobileEventDescription>
                )}
              </MobileEventCard>
            ))
        ) : (
          <MobileNoEvents>
            <div style={{ fontSize: "64px", marginBottom: "16px" }}>📅</div>
            <div
              style={{
                fontSize: "18px",
                fontWeight: "600",
                marginBottom: "8px",
              }}
            >
              No events scheduled
            </div>
            <div style={{ fontSize: "16px" }}>
              for {formatDate(currentDate, "EEEE, MMMM dd")}
            </div>
          </MobileNoEvents>
        )}
      </MobileDayEvents>

      {/* Desktop Day Grid */}
      {dayEvents.length === 0 ? (
        <NoEventsMessage>No events scheduled for this day</NoEventsMessage>
      ) : (
        <DayGrid>
          {timeSlots.map((timeSlot) => {
            const hourEvents = eventsByHour[timeSlot.hour] || [];

            return (
              <React.Fragment key={timeSlot.hour}>
                <TimeSlot>{timeSlot.label}</TimeSlot>

                <HourSlot onClick={() => handleHourClick(timeSlot.hour)}>
                  {hourEvents.map((event) => (
                    <EventBlock
                      key={event._id}
                      category={event.category}
                      onClick={(e) => handleEventClick(event, e)}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <EventTitle>{event.title}</EventTitle>
                      <EventDetails>
                        <EventTime>
                          {formatTime(event.startTime)} -{" "}
                          {formatTime(event.endTime)}
                        </EventTime>
                        {event.location?.name && (
                          <EventLocation>
                            📍 {event.location.name}
                          </EventLocation>
                        )}
                      </EventDetails>
                    </EventBlock>
                  ))}
                </HourSlot>
              </React.Fragment>
            );
          })}
        </DayGrid>
      )}
    </DayContainer>
  );
};

export default DayView;
