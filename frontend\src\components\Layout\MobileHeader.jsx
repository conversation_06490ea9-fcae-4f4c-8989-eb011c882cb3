import React, { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import styled from "styled-components";
import { motion, AnimatePresence } from "framer-motion";
import {
  ArrowLeft,
  Search,
  MoreVertical,
  Bell,
  Calendar as CalendarIcon,
  Heart,
} from "lucide-react";
import { useAuth } from "../../hooks/useAuth";
import { useStandalone } from "../../hooks/useTouch";

const HeaderContainer = styled(motion.header)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: ${({ theme }) => theme.colors.background.primary};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  padding-top: ${({ isStandalone, theme }) =>
    isStandalone ? "env(safe-area-inset-top)" : "0"};
  z-index: ${({ theme }) => theme.zIndex[40]};

  @media (min-width: 769px) {
    display: none;
  }
`;

const HeaderContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[4]};
  min-height: 56px;
`;

const LeftSection = styled.div`
  display: flex;
  align-items: center;
  flex: 1;
`;

const CenterSection = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 2;
`;

const RightSection = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const BackButton = styled(motion.button)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  color: ${({ theme }) => theme.colors.text.primary};
  cursor: pointer;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;

  &:focus {
    outline: none;
    background: ${({ theme }) => theme.colors.background.tertiary};
  }
`;

const Logo = styled(motion.div)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  color: ${({ theme }) => theme.colors.primary[600]};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  font-size: ${({ theme }) => theme.fontSizes.lg};
`;

const PageTitle = styled(motion.h1)`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
  text-align: center;
`;

const ActionButton = styled(motion.button)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;
  position: relative;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;

  &:focus {
    outline: none;
    background: ${({ theme }) => theme.colors.background.tertiary};
  }
`;

const NotificationBadge = styled.div`
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  background: ${({ theme }) => theme.colors.error[500]};
  border-radius: 50%;
  border: 2px solid ${({ theme }) => theme.colors.background.primary};
`;

const SearchBar = styled(motion.div)`
  display: flex;
  align-items: center;
  background: ${({ theme }) => theme.colors.background.tertiary};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[4]};
  margin: 0 ${({ theme }) => theme.spacing[4]};
  gap: ${({ theme }) => theme.spacing[2]};
`;

const SearchInput = styled.input`
  flex: 1;
  background: none;
  border: none;
  outline: none;
  font-size: ${({ theme }) => theme.fontSizes.base};
  color: ${({ theme }) => theme.colors.text.primary};

  &::placeholder {
    color: ${({ theme }) => theme.colors.text.tertiary};
  }
`;

const MobileHeader = ({
  showBack = false,
  title,
  showSearch = false,
  onSearch,
  showNotifications = false,
  hasNotifications = false,
  onNotificationClick,
  rightActions = [],
  currentSearchTerm = "",
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const isStandalone = useStandalone();
  const [searchQuery, setSearchQuery] = useState(currentSearchTerm);
  const [showSearchBar, setShowSearchBar] = useState(false);

  const getPageTitle = () => {
    if (title) return title;

    switch (location.pathname) {
      case "/":
        return null; // Show logo on home
      case "/calendar":
        return "Calendar";
      case "/events":
        return "Events";
      case "/profile":
        return "Profile";
      case "/admin":
        return "Admin";
      default:
        if (location.pathname.startsWith("/events/")) {
          return "Event Details";
        }
        if (location.pathname.startsWith("/admin/")) {
          return "Admin";
        }
        return "Grace Bible Fellowship";
    }
  };

  const shouldShowBack = () => {
    if (showBack) return true;
    return (
      location.pathname !== "/" &&
      location.pathname !== "/calendar" &&
      location.pathname !== "/events" &&
      location.pathname !== "/profile" &&
      location.pathname !== "/admin"
    );
  };

  const handleBack = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate("/");
    }
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    if (onSearch && searchQuery.trim()) {
      onSearch(searchQuery.trim());
    }
  };

  const toggleSearch = () => {
    setShowSearchBar(!showSearchBar);
    if (showSearchBar) {
      setSearchQuery("");
      // Clear search when closing
      if (onSearch) {
        onSearch("");
      }
    }
  };

  const pageTitle = getPageTitle();
  const showBackButton = shouldShowBack();

  console.log("MobileHeader render:", {
    showSearch,
    currentSearchTerm,
    pageTitle,
    pathname: location.pathname,
  });

  return (
    <HeaderContainer isStandalone={isStandalone}>
      <AnimatePresence mode="wait">
        {showSearchBar ? (
          <motion.form
            key="search"
            onSubmit={handleSearchSubmit}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            <SearchBar>
              <Search size={20} color="#78716c" />
              <SearchInput
                type="text"
                placeholder="Search events..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                autoFocus
              />
              <ActionButton
                type="button"
                onClick={toggleSearch}
                whileTap={{ scale: 0.95 }}
              >
                <ArrowLeft size={20} />
              </ActionButton>
            </SearchBar>
          </motion.form>
        ) : (
          <HeaderContent
            key="header"
            as={motion.div}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            <LeftSection>
              {showBackButton ? (
                <BackButton onClick={handleBack} whileTap={{ scale: 0.95 }}>
                  <ArrowLeft size={20} />
                </BackButton>
              ) : pageTitle === null ? (
                <Logo>
                  <CalendarIcon size={24} />
                  <motion.span
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 }}
                  >
                    GBF
                  </motion.span>
                </Logo>
              ) : null}
            </LeftSection>

            <CenterSection>
              {pageTitle && (
                <PageTitle
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  {pageTitle}
                </PageTitle>
              )}
            </CenterSection>

            <RightSection>
              {showSearch && (
                <ActionButton
                  onClick={toggleSearch}
                  whileTap={{ scale: 0.95 }}
                  style={{
                    background: currentSearchTerm ? "#0066cc" : "transparent",
                    color: currentSearchTerm ? "white" : "#78716c",
                  }}
                >
                  <Search size={20} />
                </ActionButton>
              )}

              {showNotifications && isAuthenticated && (
                <ActionButton
                  onClick={onNotificationClick}
                  whileTap={{ scale: 0.95 }}
                >
                  <Bell size={20} />
                  {hasNotifications && <NotificationBadge />}
                </ActionButton>
              )}

              {rightActions.map((action, index) => (
                <ActionButton
                  key={index}
                  onClick={action.onClick}
                  whileTap={{ scale: 0.95 }}
                  title={action.title}
                >
                  <action.icon size={20} />
                </ActionButton>
              ))}

              {!showBackButton && pageTitle === null && (
                <ActionButton whileTap={{ scale: 0.95 }}>
                  <Heart size={20} />
                </ActionButton>
              )}
            </RightSection>
          </HeaderContent>
        )}
      </AnimatePresence>
    </HeaderContainer>
  );
};

export default MobileHeader;
