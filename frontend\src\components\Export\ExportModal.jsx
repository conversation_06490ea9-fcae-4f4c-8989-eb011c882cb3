import React, { useState } from "react";
import styled from "styled-components";
import { motion, AnimatePresence } from "framer-motion";
import { X, Download, FileText, FileSpreadsheet, File } from "lucide-react";

const Overlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${({ theme }) => theme.spacing[4]};
`;

const Modal = styled(motion.div)`
  background: ${({ theme }) => theme.colors.background.primary};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  box-shadow: ${({ theme }) => theme.shadows.xl};
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${({ theme }) => theme.spacing[6]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
`;

const Title = styled.h2`
  margin: 0;
  font-size: ${({ theme }) => theme.fontSizes.xl};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  padding: ${({ theme }) => theme.spacing[2]};
  cursor: pointer;
  color: ${({ theme }) => theme.colors.text.secondary};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme }) => theme.colors.background.secondary};
    color: ${({ theme }) => theme.colors.text.primary};
  }
`;

const Content = styled.div`
  padding: ${({ theme }) => theme.spacing[6]};
`;

const Description = styled.p`
  margin: 0 0 ${({ theme }) => theme.spacing[6]} 0;
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  line-height: 1.5;
`;

const FormatOptions = styled.div`
  display: grid;
  gap: ${({ theme }) => theme.spacing[3]};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const FormatOption = styled.label`
  display: flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing[4]};
  border: 2px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  cursor: pointer;
  transition: all 0.2s ease;
  background: ${({ theme }) => theme.colors.background.primary};

  &:hover {
    border-color: ${({ theme }) => theme.colors.primary[400]};
    background: ${({ theme }) => theme.colors.background.secondary};
  }

  &:has(input:checked) {
    border-color: ${({ theme }) => theme.colors.primary[600]};
    background: ${({ theme }) => theme.colors.primary[50]};
  }
`;

const RadioInput = styled.input`
  margin-right: ${({ theme }) => theme.spacing[3]};
  accent-color: ${({ theme }) => theme.colors.primary[600]};
`;

const FormatIcon = styled.div`
  margin-right: ${({ theme }) => theme.spacing[3]};
  color: ${({ theme }) => theme.colors.primary[600]};
`;

const FormatInfo = styled.div`
  flex: 1;
`;

const FormatName = styled.div`
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing[1]};
`;

const FormatDescription = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const Actions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[3]};
  justify-content: flex-end;
`;

const Button = styled.button`
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[6]};
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};

  &.secondary {
    background: ${({ theme }) => theme.colors.background.secondary};
    color: ${({ theme }) => theme.colors.text.primary};

    &:hover {
      background: ${({ theme }) => theme.colors.background.tertiary};
    }
  }

  &.primary {
    background: ${({ theme }) => theme.colors.primary[600]};
    color: white;

    &:hover {
      background: ${({ theme }) => theme.colors.primary[700]};
    }

    &:disabled {
      background: ${({ theme }) => theme.colors.gray[400]};
      cursor: not-allowed;
    }
  }
`;

const ExportModal = ({ isOpen, onClose, onExport, isLoading = false }) => {
  const [selectedFormat, setSelectedFormat] = useState("csv");

  const formatOptions = [
    {
      value: "csv",
      name: "CSV (Excel)",
      description: "Spreadsheet format, perfect for data analysis and Excel",
      icon: <FileSpreadsheet size={20} />,
    },
    {
      value: "pdf",
      name: "PDF Document",
      description: "Professional document format, great for printing and sharing",
      icon: <FileText size={20} />,
    },
    {
      value: "word",
      name: "Word Document",
      description: "Editable document format, ideal for further customization",
      icon: <File size={20} />,
    },
  ];

  const handleExport = () => {
    onExport(selectedFormat);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <Overlay
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        >
          <Modal
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            onClick={(e) => e.stopPropagation()}
          >
            <Header>
              <Title>Export Events</Title>
              <CloseButton onClick={onClose}>
                <X size={20} />
              </CloseButton>
            </Header>

            <Content>
              <Description>
                Choose your preferred export format. All formats include comprehensive event details
                including dates, times, locations, contact information, and recurrence patterns.
              </Description>

              <FormatOptions>
                {formatOptions.map((option) => (
                  <FormatOption key={option.value}>
                    <RadioInput
                      type="radio"
                      name="exportFormat"
                      value={option.value}
                      checked={selectedFormat === option.value}
                      onChange={(e) => setSelectedFormat(e.target.value)}
                    />
                    <FormatIcon>{option.icon}</FormatIcon>
                    <FormatInfo>
                      <FormatName>{option.name}</FormatName>
                      <FormatDescription>{option.description}</FormatDescription>
                    </FormatInfo>
                  </FormatOption>
                ))}
              </FormatOptions>

              <Actions>
                <Button className="secondary" onClick={onClose}>
                  Cancel
                </Button>
                <Button 
                  className="primary" 
                  onClick={handleExport}
                  disabled={isLoading}
                >
                  <Download size={16} />
                  {isLoading ? "Exporting..." : "Export"}
                </Button>
              </Actions>
            </Content>
          </Modal>
        </Overlay>
      )}
    </AnimatePresence>
  );
};

export default ExportModal;
