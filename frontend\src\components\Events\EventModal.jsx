import React from "react";
import styled from "styled-components";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  Calendar,
  Clock,
  MapPin,
  Users,
  Tag,
  User,
  Edit,
  Trash2,
  Download,
  Share2,
  Mail,
  Phone,
  Repeat,
} from "lucide-react";
import { formatDate, formatTime, getDateRange } from "../../utils/dateUtils";
import { EVENT_CATEGORIES } from "../../utils/constants";
import { useAuth } from "../../hooks/useAuth";
import DocumentList from "../Common/DocumentList";

const ModalOverlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: ${({ theme }) => theme.zIndex.modal};
  padding: ${({ theme }) => theme.spacing[4]};
`;

const ModalContainer = styled(motion.div)`
  background: ${({ theme }) => theme.colors.background.primary};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  box-shadow: ${({ theme }) => theme.shadows["2xl"]};
  width: 100%;
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
`;

const ModalHeader = styled.div`
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: ${({ theme }) => theme.spacing[6]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  gap: ${({ theme }) => theme.spacing[4]};
`;

const HeaderContent = styled.div`
  flex: 1;
`;

const EventTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes["2xl"]};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0 0 ${({ theme }) => theme.spacing[2]} 0;
  line-height: ${({ theme }) => theme.lineHeights.tight};
`;

const EventSubtitle = styled.div`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: ${({ theme }) => theme.fontSizes.base};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const HeaderActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background: ${({ theme, variant }) => {
    switch (variant) {
      case "edit":
        return theme.colors.info[100];
      case "delete":
        return theme.colors.error[100];
      case "close":
        return theme.colors.background.secondary;
      default:
        return theme.colors.gray[100];
    }
  }};
  color: ${({ theme, variant }) => {
    switch (variant) {
      case "edit":
        return theme.colors.info[600];
      case "delete":
        return theme.colors.error[600];
      case "close":
        return theme.colors.text.secondary;
      default:
        return theme.colors.gray[600];
    }
  }};
  transition: all ${({ theme }) => theme.transitions.fast};

  &:hover {
    transform: scale(1.1);
    background: ${({ theme, variant }) => {
      switch (variant) {
        case "edit":
          return theme.colors.info[200];
        case "delete":
          return theme.colors.error[200];
        case "close":
          return theme.colors.error[100];
        default:
          return theme.colors.gray[200];
      }
    }};
  }
`;

const ModalBody = styled.div`
  padding: ${({ theme }) => theme.spacing[6]};
`;

const Section = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[6]};

  &:last-child {
    margin-bottom: 0;
  }
`;

const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0 0 ${({ theme }) => theme.spacing[3]} 0;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const DetailGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing[4]};

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    grid-template-columns: 1fr;
  }
`;

const DetailItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[3]};
  background: ${({ theme }) => theme.colors.background.secondary};
  border-radius: ${({ theme }) => theme.borderRadius.md};
`;

const DetailIcon = styled.div`
  color: ${({ theme }) => theme.colors.primary[500]};
  display: flex;
  align-items: center;
`;

const DetailContent = styled.div`
  flex: 1;
`;

const DetailLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.text.secondary};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: ${({ theme }) => theme.spacing[1]};
`;

const DetailValue = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.primary};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
`;

const Description = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: ${({ theme }) => theme.fontSizes.base};
  line-height: ${({ theme }) => theme.lineHeights.relaxed};
  margin: 0;
  white-space: pre-wrap;
`;

const BadgeContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const Badge = styled.span`
  display: inline-flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  background: ${({ theme, variant }) => {
    switch (variant) {
      case "category":
        return theme.colors.primary[100];
      case "status":
        return theme.colors.success[100];
      case "priority":
        return theme.colors.warning[100];
      default:
        return theme.colors.gray[100];
    }
  }};
  color: ${({ theme, variant }) => {
    switch (variant) {
      case "category":
        return theme.colors.primary[800];
      case "status":
        return theme.colors.success[800];
      case "priority":
        return theme.colors.warning[800];
      default:
        return theme.colors.gray[800];
    }
  }};
`;

const EventModal = ({ event, isOpen, onClose, onEdit, onDelete }) => {
  const { isAuthenticated, hasPermission } = useAuth();

  if (!event) return null;

  const getCategoryLabel = (categoryValue) => {
    const category = EVENT_CATEGORIES.find(
      (cat) => cat.value === categoryValue
    );
    return category ? category.label : categoryValue;
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit(event);
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(event);
    }
  };

  const canEdit = isAuthenticated && hasPermission("canEditEvents");
  const canDelete = isAuthenticated && hasPermission("canDeleteEvents");

  return (
    <AnimatePresence>
      {isOpen && (
        <ModalOverlay
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        >
          <ModalContainer
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            onClick={(e) => e.stopPropagation()}
          >
            <ModalHeader>
              <HeaderContent>
                <EventTitle>{event.title}</EventTitle>
                <EventSubtitle>
                  <Calendar size={16} />
                  {getDateRange(event.startDate, event.endDate)}
                </EventSubtitle>
              </HeaderContent>

              <HeaderActions>
                {canEdit && (
                  <ActionButton
                    variant="edit"
                    onClick={handleEdit}
                    title="Edit Event"
                  >
                    <Edit size={18} />
                  </ActionButton>
                )}
                {canDelete && (
                  <ActionButton
                    variant="delete"
                    onClick={handleDelete}
                    title="Delete Event"
                  >
                    <Trash2 size={18} />
                  </ActionButton>
                )}
                <ActionButton variant="close" onClick={onClose} title="Close">
                  <X size={18} />
                </ActionButton>
              </HeaderActions>
            </ModalHeader>

            <ModalBody>
              <Section>
                <SectionTitle>
                  <Clock size={20} />
                  Event Details
                </SectionTitle>
                <DetailGrid>
                  <DetailItem>
                    <DetailIcon>
                      <Clock size={18} />
                    </DetailIcon>
                    <DetailContent>
                      <DetailLabel>Time</DetailLabel>
                      <DetailValue>
                        {formatTime(event.startTime)} -{" "}
                        {formatTime(event.endTime)}
                      </DetailValue>
                    </DetailContent>
                  </DetailItem>

                  {event.location?.name && (
                    <DetailItem>
                      <DetailIcon>
                        <MapPin size={18} />
                      </DetailIcon>
                      <DetailContent>
                        <DetailLabel>Location</DetailLabel>
                        <DetailValue>{event.location.name}</DetailValue>
                      </DetailContent>
                    </DetailItem>
                  )}

                  {event.organizer && (
                    <DetailItem>
                      <DetailIcon>
                        <User size={18} />
                      </DetailIcon>
                      <DetailContent>
                        <DetailLabel>Organizer</DetailLabel>
                        <DetailValue>
                          {event.organizer.firstName} {event.organizer.lastName}
                        </DetailValue>
                      </DetailContent>
                    </DetailItem>
                  )}

                  {(event.pointOfContact?.name ||
                    event.pointOfContact?.email ||
                    event.pointOfContact?.phone) && (
                    <DetailItem>
                      <DetailIcon>
                        <User size={18} />
                      </DetailIcon>
                      <DetailContent>
                        <DetailLabel>Point of Contact</DetailLabel>
                        <DetailValue>
                          {event.pointOfContact.name && (
                            <div>{event.pointOfContact.name}</div>
                          )}
                          {event.pointOfContact.email && (
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                gap: "4px",
                                fontSize: "0.9em",
                                color: "#666",
                              }}
                            >
                              <Mail size={14} />
                              {event.pointOfContact.email}
                            </div>
                          )}
                          {event.pointOfContact.phone && (
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                gap: "4px",
                                fontSize: "0.9em",
                                color: "#666",
                              }}
                            >
                              <Phone size={14} />
                              {event.pointOfContact.phone}
                            </div>
                          )}
                        </DetailValue>
                      </DetailContent>
                    </DetailItem>
                  )}

                  {event.isRecurring && (
                    <DetailItem>
                      <DetailIcon>
                        <Repeat size={18} />
                      </DetailIcon>
                      <DetailContent>
                        <DetailLabel>Recurrence</DetailLabel>
                        <DetailValue>
                          {event.recurrence?.pattern && (
                            <div>
                              Every {event.recurrence.interval}{" "}
                              {event.recurrence.pattern}
                              {event.recurrence.interval > 1 ? "s" : ""}
                              {event.recurrence.pattern === "weekly" &&
                                event.recurrence.daysOfWeek?.length > 0 && (
                                  <div
                                    style={{
                                      fontSize: "0.9em",
                                      color: "#666",
                                      marginTop: "2px",
                                    }}
                                  >
                                    on{" "}
                                    {event.recurrence.daysOfWeek
                                      .map(
                                        (day) =>
                                          [
                                            "Sun",
                                            "Mon",
                                            "Tue",
                                            "Wed",
                                            "Thu",
                                            "Fri",
                                            "Sat",
                                          ][day]
                                      )
                                      .join(", ")}
                                  </div>
                                )}
                              {event.recurrence.endDate && (
                                <div
                                  style={{
                                    fontSize: "0.9em",
                                    color: "#666",
                                    marginTop: "2px",
                                  }}
                                >
                                  Until{" "}
                                  {new Date(
                                    event.recurrence.endDate
                                  ).toLocaleDateString()}
                                </div>
                              )}
                            </div>
                          )}
                        </DetailValue>
                      </DetailContent>
                    </DetailItem>
                  )}

                  {event.attendees?.expected > 0 && (
                    <DetailItem>
                      <DetailIcon>
                        <Users size={18} />
                      </DetailIcon>
                      <DetailContent>
                        <DetailLabel>Expected Attendees</DetailLabel>
                        <DetailValue>{event.attendees.expected}</DetailValue>
                      </DetailContent>
                    </DetailItem>
                  )}
                </DetailGrid>
              </Section>

              {event.description && (
                <Section>
                  <SectionTitle>Description</SectionTitle>
                  <Description>{event.description}</Description>
                </Section>
              )}

              <Section>
                <SectionTitle>
                  <Tag size={20} />
                  Categories & Status
                </SectionTitle>
                <BadgeContainer>
                  <Badge variant="category">
                    {getCategoryLabel(event.category)}
                  </Badge>
                  <Badge variant="status">{event.status}</Badge>
                  <Badge variant="priority">{event.priority} priority</Badge>
                  {event.isPublic && <Badge>Public Event</Badge>}
                </BadgeContainer>
              </Section>

              {event.attachments && event.attachments.length > 0 && (
                <Section>
                  <DocumentList documents={event.attachments} compact={true} />
                </Section>
              )}
            </ModalBody>
          </ModalContainer>
        </ModalOverlay>
      )}
    </AnimatePresence>
  );
};

export default EventModal;
