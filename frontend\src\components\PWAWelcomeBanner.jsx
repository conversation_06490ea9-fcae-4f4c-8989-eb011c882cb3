import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { motion, AnimatePresence } from "framer-motion";
import { Smartphone, X, Download, Heart, Calendar, Users } from "lucide-react";

const BannerContainer = styled(motion.div)`
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors.primary[600]} 0%,
    ${({ theme }) => theme.colors.primary[700]} 100%
  );
  color: white;
  padding: ${({ theme }) => theme.spacing[4]} ${({ theme }) => theme.spacing[4]};
  margin: ${({ theme }) => theme.spacing[3]} 0;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  box-shadow: ${({ theme }) => theme.shadows.md};
  position: relative;
  overflow: hidden;

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    margin: ${({ theme }) => theme.spacing[2]} 0;
    padding: ${({ theme }) => theme.spacing[4]}
      ${({ theme }) => theme.spacing[3]};
  }
`;

const BannerContent = styled.div`
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 2;
`;

const Title = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes["2xl"]};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  margin-bottom: ${({ theme }) => theme.spacing[3]};

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    font-size: ${({ theme }) => theme.fontSizes.xl};
  }
`;

const Description = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  opacity: 0.9;
  line-height: 1.6;

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    font-size: ${({ theme }) => theme.fontSizes.base};
    margin-bottom: ${({ theme }) => theme.spacing[4]};
  }
`;

const FeatureList = styled.div`
  display: flex;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing[6]};
  margin-bottom: ${({ theme }) => theme.spacing[6]};

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing[3]};
    margin-bottom: ${({ theme }) => theme.spacing[4]};
  }
`;

const Feature = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  opacity: 0.9;

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    justify-content: center;
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[3]};
  justify-content: center;
  align-items: center;

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing[2]};
  }
`;

const InstallButton = styled(motion.button)`
  background: white;
  color: ${({ theme }) => theme.colors.primary[600]};
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[6]};
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-height: 48px;
  touch-action: manipulation;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    width: 100%;
    justify-content: center;
    padding: ${({ theme }) => theme.spacing[3]}
      ${({ theme }) => theme.spacing[4]};
  }
`;

const DismissButton = styled.button`
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[4]};
  font-size: ${({ theme }) => theme.fontSizes.base};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px;
  touch-action: manipulation;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    width: 100%;
  }
`;

const CloseButton = styled.button`
  position: absolute;
  top: ${({ theme }) => theme.spacing[4]};
  right: ${({ theme }) => theme.spacing[4]};
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  padding: ${({ theme }) => theme.spacing[1]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  opacity: 0.7;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 1;
  }
`;

const BackgroundPattern = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  background-image: radial-gradient(
      circle at 20% 50%,
      white 2px,
      transparent 2px
    ),
    radial-gradient(circle at 80% 50%, white 2px, transparent 2px);
  background-size: 50px 50px;
  z-index: 1;
`;

const PWAWelcomeBanner = () => {
  const [deferredPrompt, setDeferredPrompt] = useState(null);
  const [showBanner, setShowBanner] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);

  useEffect(() => {
    // Check if banner was previously dismissed
    const bannerDismissed =
      localStorage.getItem("pwa-welcome-banner-dismissed") === "true";

    // Check if app is already installed
    const standalone =
      window.matchMedia("(display-mode: standalone)").matches ||
      window.navigator.standalone ||
      document.referrer.includes("android-app://");

    const wasInstalled = localStorage.getItem("pwa-installed") === "true";
    setIsInstalled(wasInstalled || standalone);

    // Track visit count to show banner only after a few visits
    const visitCount =
      parseInt(localStorage.getItem("pwa-visit-count") || "0") + 1;
    localStorage.setItem("pwa-visit-count", visitCount.toString());

    // Show banner if not dismissed, not installed, and after 2nd visit
    if (!bannerDismissed && !isInstalled && !standalone && visitCount >= 2) {
      // Delay showing banner by 3 seconds to not be intrusive
      setTimeout(() => {
        setShowBanner(true);
      }, 3000);
    }

    // Listen for install prompt
    const handleBeforeInstallPrompt = (e) => {
      e.preventDefault();
      setDeferredPrompt(e);
    };

    // Listen for app installed
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setShowBanner(false);
      localStorage.setItem("pwa-installed", "true");
    };

    window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt);
    window.addEventListener("appinstalled", handleAppInstalled);

    return () => {
      window.removeEventListener(
        "beforeinstallprompt",
        handleBeforeInstallPrompt
      );
      window.removeEventListener("appinstalled", handleAppInstalled);
    };
  }, [isInstalled]);

  const handleInstallClick = async () => {
    if (!deferredPrompt) {
      showManualInstallInstructions();
      return;
    }

    try {
      deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;

      if (outcome === "accepted") {
        setIsInstalled(true);
        setShowBanner(false);
        localStorage.setItem("pwa-installed", "true");
      }

      setDeferredPrompt(null);
    } catch (error) {
      console.error("Error during installation:", error);
      showManualInstallInstructions();
    }
  };

  const showManualInstallInstructions = () => {
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isAndroid = /Android/.test(navigator.userAgent);

    let instructions = "";
    if (isIOS) {
      instructions =
        'To install this app on your device:\n\n1. Tap the Share button (⬆️) in Safari\n2. Scroll down and tap "Add to Home Screen"\n3. Tap "Add" to confirm\n\nThe app will appear on your home screen like any other app!';
    } else if (isAndroid) {
      instructions =
        'To install this app on your device:\n\n1. Tap the menu (⋮) in your browser\n2. Tap "Add to Home screen" or "Install app"\n3. Tap "Add" to confirm\n\nThe app will appear on your home screen like any other app!';
    } else {
      instructions =
        "To install this app:\n\n1. Look for an install icon in your browser's address bar\n2. Or check your browser's menu for \"Install\" option\n3. Follow the prompts to install\n\nThe app will be available like any other desktop application!";
    }

    alert(instructions);
  };

  const dismissBanner = () => {
    setShowBanner(false);
    localStorage.setItem("pwa-welcome-banner-dismissed", "true");
  };

  if (!showBanner || isInstalled) {
    return null;
  }

  return (
    <AnimatePresence>
      <BannerContainer
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.5 }}
      >
        <BackgroundPattern />

        <CloseButton onClick={dismissBanner}>
          <X size={20} />
        </CloseButton>

        <BannerContent>
          <Title>📱 Install Grace Bible Fellowship App</Title>

          <Description>
            Get instant access to church events, calendar, and community
            updates. Install our app for the best mobile experience!
          </Description>

          <FeatureList>
            <Feature>
              <Calendar size={16} />
              Quick Event Access
            </Feature>
            <Feature>
              <Users size={16} />
              Stay Connected
            </Feature>
            <Feature>
              <Heart size={16} />
              Offline Support
            </Feature>
          </FeatureList>

          <ButtonContainer>
            <InstallButton
              onClick={handleInstallClick}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Smartphone size={20} />
              Install App Now
            </InstallButton>

            <DismissButton onClick={dismissBanner}>Maybe Later</DismissButton>
          </ButtonContainer>
        </BannerContent>
      </BannerContainer>
    </AnimatePresence>
  );
};

export default PWAWelcomeBanner;
