import React, { useState, useEffect } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import styled from "styled-components";
import { motion, AnimatePresence } from "framer-motion";
import toast from "../../utils/toast";
import {
  Menu,
  X,
  Calendar,
  User,
  LogOut,
  Settings,
  Heart,
  Home,
  Users,
  ChevronDown,
} from "lucide-react";
import { useAuth } from "../../hooks/useAuth";
import HeaderInstallButton from "../HeaderInstallButton";

const HeaderContainer = styled.header`
  background: ${({ theme }) => theme.colors.background.primary};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  position: sticky;
  top: 0;
  z-index: ${({ theme }) => theme.zIndex[50]};
  transition: all 0.3s ease;

  /* Elegant spiritual accent with church colors */
  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;

    opacity: 0.8;
  }
`;

const HeaderContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing[6]};
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
  position: relative;
  z-index: 2;

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    padding: 0 ${({ theme }) => theme.spacing[4]};
    height: 70px;
  }
`;

const LogoContainer = styled(motion.div)`
  display: flex;
  align-items: center;
  position: relative;
`;

const Logo = styled(Link)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  font-size: ${({ theme }) => theme.fontSizes["2xl"]};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.primary[700]};
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;

  /* Subtle spiritual glow on hover */
  &:hover {
    color: ${({ theme }) => theme.colors.church.worship};
    text-decoration: none;
    transform: translateY(-1px);

    /* Add gentle shadow for depth */
    text-shadow: 0 2px 8px rgba(124, 58, 237, 0.2);
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    font-size: ${({ theme }) => theme.fontSizes.xl};
    gap: ${({ theme }) => theme.spacing[2]};
  }
`;

const LogoIcon = styled(motion.div)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: ${({ theme }) => theme.colors.primary[600]};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  color: white;
  box-shadow: ${({ theme }) => theme.shadows.sm};
  transition: all 0.3s ease;
  position: relative;

  /* Gentle spiritual glow effect */
  &:hover {
    background: ${({ theme }) => theme.colors.church.worship};
    box-shadow: 0 4px 20px rgba(124, 58, 237, 0.3);
    transform: translateY(-1px);
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    width: 40px;
    height: 40px;

    svg {
      width: 20px;
      height: 20px;
    }
  }
`;

const LogoText = styled.span`
  color: ${({ theme }) => theme.colors.primary[700]};
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    display: none;
  }
`;

const LogoTextMobile = styled.span`
  display: none;
  color: ${({ theme }) => theme.colors.primary[700]};
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    display: block;
  }
`;

const Nav = styled.nav`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  background: ${({ theme }) => theme.colors.background.accent};
  padding: ${({ theme }) => theme.spacing[1]};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  border: 1px solid ${({ theme }) => theme.colors.border.accent};

  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    display: none;
  }
`;

const NavItem = styled.div`
  position: relative;
  display: flex;
  align-items: center;
`;

const NavLink = styled(Link)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[4]};
  color: ${({ isActive, theme }) =>
    isActive ? "white" : theme.colors.text.secondary};
  text-decoration: none;
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  transition: all 0.3s ease;
  background: ${({ isActive, theme }) =>
    isActive ? theme.colors.primary[600] : "transparent"};

  &:hover {
    color: ${({ isActive, theme }) =>
      isActive ? "white" : theme.colors.primary[600]};
    text-decoration: none;
    background: ${({ isActive, theme }) =>
      isActive ? theme.colors.primary[700] : theme.colors.primary[50]};
  }

  svg {
    width: 16px;
    height: 16px;
  }
`;

const UserMenu = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const ActionButton = styled(motion.button)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: ${({ theme }) => theme.colors.text.secondary};
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: linear-gradient(
      135deg,
      ${({ theme }) => theme.colors.primary[500]},
      ${({ theme }) => theme.colors.secondary[500]}
    );
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
    color: white;

    &::before {
      opacity: 1;
    }

    svg {
      position: relative;
      z-index: 2;
    }
  }

  svg {
    width: 20px;
    height: 20px;
    transition: all 0.3s ease;
  }
`;

const UserButton = styled(motion.button)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[4]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  background: ${({ theme }) => theme.colors.primary[600]};
  border: none;
  color: white;
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    background: ${({ theme }) => theme.colors.primary[700]};
    transform: translateY(-1px);
  }

  svg {
    width: 18px;
    height: 18px;
  }
`;

const MobileMenuButton = styled(motion.button)`
  display: none;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  background: ${({ theme }) => theme.colors.background.secondary};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  color: ${({ theme }) => theme.colors.text.primary};
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    background: ${({ theme }) => theme.colors.primary[50]};
    color: ${({ theme }) => theme.colors.primary[600]};
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    display: flex;
  }
`;

const MobileMenu = styled(motion.div)`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: ${({ theme }) => theme.colors.background.primary};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  box-shadow: ${({ theme }) => theme.shadows.lg};
  padding: ${({ theme }) => theme.spacing[4]};
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[4]};
`;

const DropdownMenu = styled(motion.div)`
  position: absolute;
  top: 100%;
  right: 0;
  background: ${({ theme }) => theme.colors.background.primary};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  box-shadow: ${({ theme }) => theme.shadows.lg};
  padding: ${({ theme }) => theme.spacing[2]};
  min-width: 200px;
  z-index: ${({ theme }) => theme.zIndex.dropdown};
`;

const DropdownItem = styled.button`
  width: 100%;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background: transparent;
  color: ${({ theme }) => theme.colors.text.primary};
  text-align: left;
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background: ${({ theme }) => theme.colors.background.secondary};
  }
`;

const LoginButton = styled(motion(Link))`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[5]};
  background: ${({ theme }) => theme.colors.primary[600]};
  color: white;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  text-decoration: none;
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  transition: all 0.3s ease;

  &:hover {
    background: ${({ theme }) => theme.colors.primary[700]};
    text-decoration: none;
    transform: translateY(-1px);
  }
`;

const Header = () => {
  const { isAuthenticated, user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleLogout = async () => {
    await logout();
    navigate("/");
    setShowUserMenu(false);
    toast.success("Logged out successfully! 👋");
  };

  const navItems = [
    { to: "/", label: "Home", icon: Home },
    { to: "/calendar", label: "Calendar", icon: Calendar },
    { to: "/events", label: "Events", icon: Users },
  ];

  const isActivePath = (path) => {
    if (path === "/") {
      return location.pathname === "/";
    }
    return location.pathname.startsWith(path);
  };

  return (
    <HeaderContainer>
      <HeaderContent>
        <LogoContainer
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Logo to="/">
            <LogoIcon
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.6 }}
            >
              <Heart size={24} />
            </LogoIcon>
            <LogoText>Grace Bible Fellowship</LogoText>
            <LogoTextMobile>GBF</LogoTextMobile>
          </Logo>
        </LogoContainer>

        <Nav>
          {navItems.map((item, index) => {
            const IconComponent = item.icon;
            const isActive = isActivePath(item.to);
            return (
              <NavItem key={item.to}>
                <NavLink
                  to={item.to}
                  isActive={isActive}
                  as={motion(Link)}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <IconComponent size={16} />
                  {item.label}
                </NavLink>
              </NavItem>
            );
          })}
        </Nav>

        <UserMenu>
          <HeaderInstallButton />

          {isAuthenticated ? (
            <div style={{ position: "relative" }}>
              <UserButton
                onClick={() => setShowUserMenu(!showUserMenu)}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <User size={18} />
                <span>{user?.firstName || "User"}</span>
                <ChevronDown size={14} />
              </UserButton>

              <AnimatePresence>
                {showUserMenu && (
                  <DropdownMenu
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                  >
                    <DropdownItem
                      onClick={() => {
                        navigate("/profile");
                        setShowUserMenu(false);
                      }}
                    >
                      <User size={16} />
                      Profile
                    </DropdownItem>
                    {user?.role === "admin" && (
                      <DropdownItem
                        onClick={() => {
                          navigate("/admin");
                          setShowUserMenu(false);
                        }}
                      >
                        <Settings size={16} />
                        Admin
                      </DropdownItem>
                    )}
                    <DropdownItem onClick={handleLogout}>
                      <LogOut size={16} />
                      Logout
                    </DropdownItem>
                  </DropdownMenu>
                )}
              </AnimatePresence>
            </div>
          ) : (
            <LoginButton
              to="/login"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <span>Login</span>
            </LoginButton>
          )}

          <MobileMenuButton
            onClick={() => setShowMobileMenu(!showMobileMenu)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {showMobileMenu ? <X size={20} /> : <Menu size={20} />}
          </MobileMenuButton>
        </UserMenu>
      </HeaderContent>

      <AnimatePresence>
        {showMobileMenu && (
          <MobileMenu
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            {navItems.map((item) => (
              <NavLink
                key={item.to}
                to={item.to}
                onClick={() => setShowMobileMenu(false)}
              >
                {item.label}
              </NavLink>
            ))}

            <div style={{ padding: "16px", borderTop: "1px solid #e5e7eb" }}>
              <HeaderInstallButton />
            </div>
          </MobileMenu>
        )}
      </AnimatePresence>
    </HeaderContainer>
  );
};

export default Header;
