import { create } from "zustand";
import { persist } from "zustand/middleware";
import { authAPI } from "../api";

export const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials) => {
        set({ isLoading: true, error: null });
        try {
          const response = await authAPI.login(credentials);
          const { user, token } = response.data;

          // Store token in localStorage
          localStorage.setItem("token", token);

          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          return response;
        } catch (error) {
          const errorMessage = error.response?.data?.message || "Login failed";
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });
          throw error;
        }
      },

      logout: async () => {
        try {
          await authAPI.logout();
        } catch (error) {
          console.error("Logout error:", error);
        } finally {
          // Clear local storage
          localStorage.removeItem("token");
          localStorage.removeItem("user");

          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      },

      updateProfile: async (profileData) => {
        set({ isLoading: true, error: null });
        try {
          const response = await authAPI.updateProfile(profileData);
          const { user } = response.data;

          set({
            user,
            isLoading: false,
            error: null,
          });

          return response;
        } catch (error) {
          const errorMessage =
            error.response?.data?.message || "Profile update failed";
          set({
            isLoading: false,
            error: errorMessage,
          });
          throw error;
        }
      },

      changePassword: async (passwordData) => {
        set({ isLoading: true, error: null });
        try {
          const response = await authAPI.changePassword(passwordData);
          set({
            isLoading: false,
            error: null,
          });
          return response;
        } catch (error) {
          const errorMessage =
            error.response?.data?.message || "Password change failed";
          set({
            isLoading: false,
            error: errorMessage,
          });
          throw error;
        }
      },

      verifyToken: async () => {
        const token = localStorage.getItem("token");
        if (!token) {
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
          });
          return false;
        }

        set({ isLoading: true });
        try {
          const response = await authAPI.verifyToken();
          const { user } = response.data;

          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          return true;
        } catch (error) {
          localStorage.removeItem("token");
          localStorage.removeItem("user");

          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });

          return false;
        }
      },

      clearError: () => {
        set({ error: null });
      },

      // Getters
      isAdmin: () => {
        const { user } = get();
        return user?.role === "admin";
      },

      isEditor: () => {
        const { user } = get();
        return user?.role === "editor";
      },

      hasPermission: (permission) => {
        const { user } = get();
        return user?.permissions?.[permission] || false;
      },
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
