import {
  format,
  parseISO,
  isValid,
  startOfDay,
  endOfDay,
  addDays,
  subDays,
} from "date-fns";

// Format date for display
export const formatDate = (date, formatString = "MMM dd, yyyy") => {
  if (!date) return "";

  const dateObj = typeof date === "string" ? parseISO(date) : date;

  if (!isValid(dateObj)) return "";

  return format(dateObj, formatString);
};

// Format time for display
export const formatTime = (time) => {
  if (!time) return "";

  // If time is in HH:MM format, convert to 12-hour format
  const [hours, minutes] = time.split(":");
  const hour = parseInt(hours, 10);
  const ampm = hour >= 12 ? "PM" : "AM";
  const displayHour = hour % 12 || 12;

  return `${displayHour}:${minutes} ${ampm}`;
};

// Format date and time together
export const formatDateTime = (date, time) => {
  const formattedDate = formatDate(date, "MMM dd, yyyy");
  const formattedTime = formatTime(time);

  return `${formattedDate} at ${formattedTime}`;
};

// Get date range string
export const getDateRange = (startDate, endDate) => {
  if (!startDate || !endDate) return "";

  const start = formatDate(startDate, "MMM dd");
  const end = formatDate(endDate, "MMM dd, yyyy");

  if (
    formatDate(startDate, "yyyy-MM-dd") === formatDate(endDate, "yyyy-MM-dd")
  ) {
    return formatDate(startDate, "MMM dd, yyyy");
  }

  return `${start} - ${end}`;
};

// Check if date is today
export const isToday = (date) => {
  if (!date) return false;

  const dateObj = typeof date === "string" ? parseISO(date) : date;
  const today = new Date();

  return formatDate(dateObj, "yyyy-MM-dd") === formatDate(today, "yyyy-MM-dd");
};

// Check if date is in the past
export const isPast = (date) => {
  if (!date) return false;

  const dateObj = typeof date === "string" ? parseISO(date) : date;
  const today = startOfDay(new Date());

  return dateObj < today;
};

// Check if date is in the future
export const isFuture = (date) => {
  if (!date) return false;

  const dateObj = typeof date === "string" ? parseISO(date) : date;
  const today = endOfDay(new Date());

  return dateObj > today;
};

// Get relative date string (e.g., "Today", "Tomorrow", "Yesterday")
export const getRelativeDate = (date) => {
  if (!date) return "";

  const dateObj = typeof date === "string" ? parseISO(date) : date;
  const today = new Date();
  const tomorrow = addDays(today, 1);
  const yesterday = subDays(today, 1);

  if (formatDate(dateObj, "yyyy-MM-dd") === formatDate(today, "yyyy-MM-dd")) {
    return "Today";
  }

  if (
    formatDate(dateObj, "yyyy-MM-dd") === formatDate(tomorrow, "yyyy-MM-dd")
  ) {
    return "Tomorrow";
  }

  if (
    formatDate(dateObj, "yyyy-MM-dd") === formatDate(yesterday, "yyyy-MM-dd")
  ) {
    return "Yesterday";
  }

  return formatDate(dateObj, "MMM dd, yyyy");
};

// Convert date to ISO string for API
export const toISOString = (date) => {
  if (!date) return "";

  const dateObj = typeof date === "string" ? parseISO(date) : date;

  if (!isValid(dateObj)) return "";

  return dateObj.toISOString();
};

// Get current date in YYYY-MM-DD format
export const getCurrentDate = () => {
  return format(new Date(), "yyyy-MM-dd");
};

// Get current time in HH:MM format
export const getCurrentTime = () => {
  return format(new Date(), "HH:mm");
};

// Convert Date object to YYYY-MM-DD string without timezone issues
export const formatDateForInput = (date) => {
  if (!date) return "";

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
};

// Parse date from input value
export const parseInputDate = (value) => {
  if (!value) return null;

  const date = parseISO(value);
  return isValid(date) ? date : null;
};

// Get month name
export const getMonthName = (monthIndex) => {
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  return months[monthIndex] || "";
};

// Get day name
export const getDayName = (dayIndex) => {
  const days = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];

  return days[dayIndex] || "";
};

// Get days in month
export const getDaysInMonth = (year, month) => {
  return new Date(year, month + 1, 0).getDate();
};

// Get first day of month (0 = Sunday, 1 = Monday, etc.)
export const getFirstDayOfMonth = (year, month) => {
  return new Date(year, month, 1).getDay();
};

// Convert local date to UTC date string for API submission (prevents timezone issues)
export const toUTCDateOnly = (dateString) => {
  if (!dateString) return null;

  // Create date at noon to avoid timezone shifting to previous/next day
  const localDate = new Date(`${dateString}T12:00:00`);

  // Return just the date part in ISO format (YYYY-MM-DD)
  return localDate.toISOString().split("T")[0];
};

// Convert local date and time to full UTC datetime for API submission
export const toUTCDateTime = (dateString, timeString = "00:00") => {
  if (!dateString) return null;

  // Create date in local timezone
  const localDate = new Date(`${dateString}T${timeString}`);

  // Return ISO string which will be in UTC
  return localDate.toISOString();
};

// Convert UTC date from API to local date string for form inputs
export const fromUTCDate = (utcDateString) => {
  if (!utcDateString) return "";

  const date = new Date(utcDateString);

  // Return local date in YYYY-MM-DD format
  return format(date, "yyyy-MM-dd");
};

// Convert UTC datetime to local time string for form inputs
export const fromUTCTime = (utcDateString) => {
  if (!utcDateString) return "";

  const date = new Date(utcDateString);

  // Return local time in HH:mm format
  return format(date, "HH:mm");
};
