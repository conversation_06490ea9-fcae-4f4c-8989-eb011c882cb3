# Production Deployment Guide

## Environment Configuration

### 1. Update Environment Variables

For production deployment, update the following environment variables:

#### Required Production Variables:

```bash
NODE_ENV=production
MONGODB_URI=mongodb+srv://username:<EMAIL>/church-events
JWT_SECRET=your-super-secure-production-jwt-secret
FRONTEND_URL=https://your-production-domain.com
PRODUCTION_FRONTEND_URL=https://your-production-domain.com
```

#### Optional Production Variables:

```bash
STAGING_FRONTEND_URL=https://staging.your-domain.com
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-specific-password
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### 2. CORS Configuration

The application automatically handles CORS for different environments:

- **Development**: Allows all localhost origins
- **Production**: Only allows explicitly configured domains

#### Adding New Production Domains:

1. **Environment Variables** (Recommended):

   ```bash
   PRODUCTION_FRONTEND_URL=https://your-new-domain.com
   STAGING_FRONTEND_URL=https://staging.your-domain.com
   ```

2. **Direct Code Update** (if needed):
   ```javascript
   const allowedOrigins = [
     // Add your domains here
     "https://your-app.vercel.app",
     "https://your-app.netlify.app",
     "https://your-custom-domain.com",
   ];
   ```

## Common Deployment Platforms

### 1. Heroku

```bash
# Set environment variables
heroku config:set NODE_ENV=production
heroku config:set MONGODB_URI=your-mongodb-connection-string
heroku config:set JWT_SECRET=your-jwt-secret
heroku config:set FRONTEND_URL=https://your-frontend-domain.com
```

### 2. Railway

```bash
# In Railway dashboard, add environment variables:
NODE_ENV=production
MONGODB_URI=mongodb+srv://...
FRONTEND_URL=https://your-frontend.vercel.app
```

### 3. Render

```bash
# In Render dashboard, add environment variables:
NODE_ENV=production
MONGODB_URI=mongodb+srv://...
FRONTEND_URL=https://your-frontend.netlify.app
```

### 4. DigitalOcean App Platform

```yaml
# app.yaml
envs:
  - key: NODE_ENV
    value: production
  - key: MONGODB_URI
    value: mongodb+srv://...
  - key: FRONTEND_URL
    value: https://your-frontend-domain.com
```

## Frontend Deployment Considerations

### Update Frontend API URL

In your frontend `.env.production`:

```bash
VITE_API_URL=https://your-backend-domain.com/api
# or
REACT_APP_API_URL=https://your-backend-domain.com/api
```

### Common Frontend + Backend Combinations:

1. **Vercel (Frontend) + Railway (Backend)**:

   ```bash
   # Backend (Railway)
   FRONTEND_URL=https://your-app.vercel.app

   # Frontend (Vercel)
   VITE_API_URL=https://your-app.railway.app/api
   ```

2. **Netlify (Frontend) + Heroku (Backend)**:

   ```bash
   # Backend (Heroku)
   FRONTEND_URL=https://your-app.netlify.app

   # Frontend (Netlify)
   VITE_API_URL=https://your-app.herokuapp.com/api
   ```

## Security Checklist

- [ ] Use strong JWT_SECRET (minimum 32 characters)
- [ ] Use production MongoDB cluster with authentication
- [ ] Set NODE_ENV=production
- [ ] Configure proper CORS origins
- [ ] Use HTTPS for all production URLs
- [ ] Set up proper email credentials for notifications
- [ ] Configure rate limiting for production traffic

## Testing Production CORS

1. Deploy backend with production environment variables
2. Test CORS from browser console:
   ```javascript
   fetch("https://your-backend-domain.com/api/health", {
     method: "GET",
     credentials: "include",
   })
     .then((r) => r.json())
     .then(console.log);
   ```

## Troubleshooting

### CORS Issues:

1. Check that FRONTEND_URL matches exactly (including https://)
2. Verify environment variables are loaded correctly
3. Check browser network tab for preflight requests
4. Ensure credentials: true is set on frontend requests

### Environment Variables Not Loading:

1. Verify .env file is in correct location
2. Check that dotenv is configured properly
3. Use console.log to verify variables are loaded
4. For cloud platforms, set variables in dashboard/CLI
