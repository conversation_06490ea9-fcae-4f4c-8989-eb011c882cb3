{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "dev:clean": "npm run clean && npm run dev", "dev:force": "npm run clean && vite --force --host 0.0.0.0 --port 3000", "build": "vite build", "build:analyze": "vite build --mode analyze", "preview": "vite preview --host 0.0.0.0 --port 3000", "lint": "eslint . --ext js,jsx,ts,tsx", "lint:fix": "eslint . --ext js,jsx,ts,tsx --fix", "clean": "rimraf node_modules/.vite dist", "clean:all": "rimraf node_modules/.vite dist node_modules && npm install", "clean:cache": "rimraf node_modules/.vite", "type-check": "tsc --noEmit", "serve": "npm run build && npm run preview", "test-pwa": "npm run build && npx serve dist -s -l 3000", "test-pwa-https": "npm run build && npx serve dist -s -l 3000 --ssl-cert", "preview-https": "vite preview --https"}, "dependencies": {"axios": "^1.11.0", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "lucide-react": "^0.539.0", "moment": "^2.30.1", "react": "^18.3.1", "react-big-calendar": "^1.19.4", "react-datepicker": "^8.4.0", "react-dom": "^18.3.1", "react-router-dom": "^7.8.0", "react-toastify": "^11.0.5", "styled-components": "^6.1.19", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.3.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "rimraf": "^6.0.1", "serve": "^14.2.4", "vite": "^5.4.0", "vite-plugin-pwa": "^1.0.2", "workbox-window": "^7.3.0"}}