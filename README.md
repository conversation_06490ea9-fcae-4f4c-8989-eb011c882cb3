# Grace Bible Fellowship System

A comprehensive, mobile-first event management system designed specifically for churches. This system provides different access levels for administrators, editors, and viewers (church members).

## Features

### For Viewers (Church Members)

- View all published church events
- Advanced calendar with multiple view types (Month, Week, Day, Agenda)
- Clean, mobile-friendly interface
- No login required
- Event details and information
- Responsive design for all devices

### For Editors

- Create, edit, and delete events
- Export event data
- Manage event details and resources
- Access to admin calendar features
- User authentication required

### For Administrators

- All editor permissions
- User management (create, edit, delete editors)
- Advanced permissions management
- System administration
- Full access to all features

## Technology Stack

### Frontend

- **React** with Vite for fast development
- **Styled Components** for styling
- **Zustand** for state management
- **Axios** for API communication
- **Framer Motion** for animations
- **React Router** for navigation
- **React Big Calendar** for calendar functionality

### Backend

- **Node.js** with Express
- **MongoDB** with Mongoose
- **JWT** for authentication
- **bcryptjs** for password hashing
- **Express Validator** for input validation
- **Helmet** for security
- **CORS** for cross-origin requests

## Project Structure

```
church-management/
├── backend/
│   ├── controllers/
│   ├── middleware/
│   ├── models/
│   ├── routes/
│   ├── scripts/
│   ├── utils/
│   ├── uploads/
│   ├── server.js
│   └── package.json
├── frontend/
│   ├── src/
│   │   ├── api/
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── pages/
│   │   ├── store/
│   │   ├── styles/
│   │   ├── utils/
│   │   ├── App.jsx
│   │   └── main.jsx
│   ├── public/
│   └── package.json
└── README.md
```

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- MongoDB (local or cloud instance)
- npm or yarn

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd church-management
   ```

2. **Install all dependencies (monorepo setup)**

   ```bash
   npm run install:all
   ```

3. **Set up environment files**

   ```bash
   # Backend environment
   cp backend/.env.example backend/.env
   # Edit backend/.env file with your MongoDB URI and other settings

   # Frontend environment
   cp frontend/.env.example frontend/.env
   # Edit frontend/.env file if needed
   ```

4. **Start the application**
   ```bash
   npm start
   ```
   This will:
   - Create the initial admin user
   - Start both backend and frontend servers concurrently

### Default Admin Credentials

- **Email**: <EMAIL>
- **Password**: admin123

**Important**: Change these credentials after first login!

## API Endpoints

### Authentication

- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user
- `PUT /api/auth/profile` - Update profile
- `PUT /api/auth/change-password` - Change password

### Events

- `GET /api/events` - Get all events (public)
- `GET /api/events/:id` - Get single event
- `POST /api/events` - Create event (auth required)
- `PUT /api/events/:id` - Update event (auth required)
- `DELETE /api/events/:id` - Delete event (auth required)
- `GET /api/events/calendar/:year/:month` - Get calendar events
- `GET /api/events/export/data` - Export events (auth required)

### Users (Admin only)

- `GET /api/users` - Get all users
- `GET /api/users/:id` - Get single user
- `POST /api/users` - Create user
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user

## Development

### Available Scripts

**Start everything (recommended):**

```bash
npm start                 # Seeds admin user and starts both servers
```

**Development commands:**

```bash
npm run dev              # Start both servers concurrently
npm run backend          # Start only backend server
npm run frontend         # Start only frontend server
npm run install:all      # Install all dependencies
npm run seed:admin       # Create admin user
```

**Other commands:**

```bash
npm run build           # Build frontend for production
npm run clean           # Remove all node_modules
npm run lint            # Run linting on both projects
npm run test            # Run tests on both projects
```

### Building for Production

**Frontend:**

```bash
cd frontend
npm run build
```

**Backend:**

```bash
cd backend
npm start
```

## Environment Variables

### Backend (.env)

```
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/church-events
JWT_SECRET=your-secret-key
JWT_EXPIRE=7d
FRONTEND_URL=http://localhost:5173
```

### Frontend (.env)

```
VITE_API_URL=http://localhost:5000/api
VITE_APP_NAME=Grace Bible Fellowship
VITE_APP_VERSION=1.0.0
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please contact the development team.
