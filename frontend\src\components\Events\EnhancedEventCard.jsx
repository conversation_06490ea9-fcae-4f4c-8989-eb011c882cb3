import React, { useState } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Calendar, 
  Clock, 
  MapPin, 
  Users, 
  Heart,
  Share2,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { useTouchInteraction } from '../../hooks/useHapticFeedback';
import { useTouch } from '../../hooks/useTouch';
import { formatDate, formatTime, isToday, isTomorrow, isPast } from '../../utils/dateUtils';
import { EVENT_CATEGORIES } from '../../utils/constants';

const CardContainer = styled(motion.div)`
  background: ${({ theme }) => theme.colors.background.card};
  border-radius: ${({ theme }) => theme.borderRadius['2xl']};
  overflow: hidden;
  box-shadow: ${({ theme }) => theme.shadows.md};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  position: relative;
  cursor: pointer;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;

  &:hover {
    box-shadow: ${({ theme }) => theme.shadows.lg};
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    margin-bottom: ${({ theme }) => theme.spacing[3]};
    border-radius: ${({ theme }) => theme.borderRadius.xl};
  }
`;

const CategoryStripe = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: ${({ theme, category }) => {
    const categoryData = EVENT_CATEGORIES.find(cat => cat.value === category);
    return categoryData ? theme.colors.church[categoryData.value] || theme.colors.primary[500] : theme.colors.gray[400];
  }};
`;

const CardContent = styled.div`
  padding: ${({ theme }) => theme.spacing[5]};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    padding: ${({ theme }) => theme.spacing[4]};
  }
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${({ theme }) => theme.spacing[3]};
`;

const EventTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
  line-height: 1.3;
  flex: 1;
  margin-right: ${({ theme }) => theme.spacing[3]};

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    font-size: ${({ theme }) => theme.fontSizes['mobile-lg']};
  }
`;

const QuickActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[1]};
  opacity: 0;
  transition: opacity ${({ theme }) => theme.transitions.fast};

  ${CardContainer}:hover & {
    opacity: 1;
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    opacity: 1; /* Always visible on mobile */
  }
`;

const ActionButton = styled(motion.button)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  border: none;
  background: ${({ theme, variant }) => {
    switch (variant) {
      case 'like': return theme.colors.error[50];
      case 'share': return theme.colors.primary[50];
      case 'edit': return theme.colors.secondary[50];
      case 'delete': return theme.colors.error[50];
      default: return theme.colors.gray[100];
    }
  }};
  color: ${({ theme, variant }) => {
    switch (variant) {
      case 'like': return theme.colors.error[600];
      case 'share': return theme.colors.primary[600];
      case 'edit': return theme.colors.secondary[600];
      case 'delete': return theme.colors.error[600];
      default: return theme.colors.gray[600];
    }
  }};
  cursor: pointer;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary[200]};
  }
`;

const EventMeta = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${({ theme }) => theme.spacing[3]};
  margin-bottom: ${({ theme }) => theme.spacing[4]};

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    grid-template-columns: 1fr;
    gap: ${({ theme }) => theme.spacing[2]};
  }
`;

const MetaItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.secondary};

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    font-size: ${({ theme }) => theme.fontSizes['mobile-sm']};
  }
`;

const MetaIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: ${({ theme }) => theme.colors.text.tertiary};
`;

const EventDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.secondary};
  line-height: 1.5;
  margin: 0 0 ${({ theme }) => theme.spacing[3]} 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    font-size: ${({ theme }) => theme.fontSizes['mobile-sm']};
    -webkit-line-clamp: 3;
  }
`;

const DateBadge = styled.div`
  position: absolute;
  top: ${({ theme }) => theme.spacing[4]};
  right: ${({ theme }) => theme.spacing[4]};
  background: ${({ theme, isToday, isTomorrow }) => {
    if (isToday) return theme.colors.success[500];
    if (isTomorrow) return theme.colors.warning[500];
    return theme.colors.primary[500];
  }};
  color: white;
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const SwipeIndicator = styled(motion.div)`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: ${({ theme }) => theme.colors.primary[500]};
  color: white;
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  z-index: 10;
  pointer-events: none;

  &.left {
    left: ${({ theme }) => theme.spacing[4]};
  }

  &.right {
    right: ${({ theme }) => theme.spacing[4]};
  }
`;

const EnhancedEventCard = ({ 
  event, 
  onClick, 
  onEdit, 
  onDelete, 
  onLike, 
  onShare,
  showActions = true 
}) => {
  const { isAuthenticated, user } = useAuth();
  const { handlePress, handleLongPress, haptic } = useTouchInteraction();
  const [showSwipeIndicator, setShowSwipeIndicator] = useState(null);
  const [isLiked, setIsLiked] = useState(false);

  const eventDate = new Date(event.date);
  const eventIsToday = isToday(eventDate);
  const eventIsTomorrow = isTomorrow(eventDate);
  const eventIsPast = isPast(eventDate);

  const canEdit = isAuthenticated && (user?.role === 'admin' || user?.role === 'editor' || user?.id === event.createdBy);
  const canDelete = isAuthenticated && (user?.role === 'admin' || user?.id === event.createdBy);

  const { touchRef } = useTouch({
    onSwipeLeft: () => {
      if (canEdit) {
        setShowSwipeIndicator('edit');
        haptic.medium();
        setTimeout(() => {
          setShowSwipeIndicator(null);
          if (onEdit) onEdit(event);
        }, 300);
      }
    },
    onSwipeRight: () => {
      setShowSwipeIndicator('like');
      haptic.light();
      setTimeout(() => {
        setShowSwipeIndicator(null);
        handleLike();
      }, 300);
    },
    onTap: () => {
      if (onClick) onClick(event);
    },
    onLongPress: () => {
      haptic.heavy();
      // Show context menu or additional options
    },
    threshold: 50
  });

  const handleLike = () => {
    setIsLiked(!isLiked);
    haptic.success();
    if (onLike) onLike(event);
  };

  const handleShare = () => {
    haptic.light();
    if (onShare) onShare(event);
  };

  const handleEdit = () => {
    haptic.medium();
    if (onEdit) onEdit(event);
  };

  const handleDelete = () => {
    haptic.warning();
    if (onDelete) onDelete(event);
  };

  const getDateBadgeText = () => {
    if (eventIsToday) return 'Today';
    if (eventIsTomorrow) return 'Tomorrow';
    if (eventIsPast) return 'Past';
    return formatDate(eventDate, 'MMM dd');
  };

  return (
    <CardContainer
      ref={touchRef}
      whileHover={{ y: -2 }}
      whileTap={{ scale: 0.98 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      style={{ opacity: eventIsPast ? 0.8 : 1 }}
    >
      <CategoryStripe category={event.category} />
      
      <DateBadge 
        isToday={eventIsToday} 
        isTomorrow={eventIsTomorrow}
      >
        {getDateBadgeText()}
      </DateBadge>

      <CardContent>
        <CardHeader>
          <EventTitle>{event.title}</EventTitle>
          
          {showActions && (
            <QuickActions>
              <ActionButton
                variant="like"
                onClick={handleLike}
                whileTap={{ scale: 0.9 }}
                style={{ 
                  background: isLiked ? '#ef4444' : undefined,
                  color: isLiked ? 'white' : undefined 
                }}
              >
                <Heart size={16} fill={isLiked ? 'currentColor' : 'none'} />
              </ActionButton>
              
              <ActionButton
                variant="share"
                onClick={handleShare}
                whileTap={{ scale: 0.9 }}
              >
                <Share2 size={16} />
              </ActionButton>

              {canEdit && (
                <ActionButton
                  variant="edit"
                  onClick={handleEdit}
                  whileTap={{ scale: 0.9 }}
                >
                  <Edit size={16} />
                </ActionButton>
              )}
            </QuickActions>
          )}
        </CardHeader>

        {event.description && (
          <EventDescription>{event.description}</EventDescription>
        )}

        <EventMeta>
          <MetaItem>
            <MetaIcon>
              <Clock size={16} />
            </MetaIcon>
            {formatTime(event.startTime)} - {formatTime(event.endTime)}
          </MetaItem>

          {event.location?.name && (
            <MetaItem>
              <MetaIcon>
                <MapPin size={16} />
              </MetaIcon>
              {event.location.name}
            </MetaItem>
          )}

          {event.attendees?.expected > 0 && (
            <MetaItem>
              <MetaIcon>
                <Users size={16} />
              </MetaIcon>
              {event.attendees.expected} expected
            </MetaItem>
          )}
        </EventMeta>
      </CardContent>

      <AnimatePresence>
        {showSwipeIndicator && (
          <SwipeIndicator
            className={showSwipeIndicator === 'like' ? 'right' : 'left'}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2 }}
          >
            {showSwipeIndicator === 'like' ? '❤️ Liked!' : '✏️ Edit'}
          </SwipeIndicator>
        )}
      </AnimatePresence>
    </CardContainer>
  );
};

export default EnhancedEventCard;
