import { useCallback } from 'react';

/**
 * Hook for haptic feedback simulation and native haptic feedback
 * Provides different types of haptic feedback for mobile interactions
 */
export const useHapticFeedback = () => {
  // Check if the device supports haptic feedback
  const supportsHaptics = useCallback(() => {
    return (
      'vibrate' in navigator ||
      'hapticFeedback' in navigator ||
      (window.DeviceMotionEvent && typeof DeviceMotionEvent.requestPermission === 'function')
    );
  }, []);

  // Light haptic feedback for subtle interactions
  const light = useCallback(() => {
    try {
      // Try native haptic feedback first (iOS)
      if (navigator.hapticFeedback) {
        navigator.hapticFeedback.impact('light');
        return;
      }

      // Fallback to vibration API
      if (navigator.vibrate) {
        navigator.vibrate(10); // Very short vibration
      }
    } catch (error) {
      console.debug('Haptic feedback not available:', error);
    }
  }, []);

  // Medium haptic feedback for button presses
  const medium = useCallback(() => {
    try {
      if (navigator.hapticFeedback) {
        navigator.hapticFeedback.impact('medium');
        return;
      }

      if (navigator.vibrate) {
        navigator.vibrate(25);
      }
    } catch (error) {
      console.debug('Haptic feedback not available:', error);
    }
  }, []);

  // Heavy haptic feedback for important actions
  const heavy = useCallback(() => {
    try {
      if (navigator.hapticFeedback) {
        navigator.hapticFeedback.impact('heavy');
        return;
      }

      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    } catch (error) {
      console.debug('Haptic feedback not available:', error);
    }
  }, []);

  // Success haptic feedback
  const success = useCallback(() => {
    try {
      if (navigator.hapticFeedback) {
        navigator.hapticFeedback.notification('success');
        return;
      }

      if (navigator.vibrate) {
        navigator.vibrate([25, 50, 25]); // Pattern for success
      }
    } catch (error) {
      console.debug('Haptic feedback not available:', error);
    }
  }, []);

  // Warning haptic feedback
  const warning = useCallback(() => {
    try {
      if (navigator.hapticFeedback) {
        navigator.hapticFeedback.notification('warning');
        return;
      }

      if (navigator.vibrate) {
        navigator.vibrate([50, 25, 50]); // Pattern for warning
      }
    } catch (error) {
      console.debug('Haptic feedback not available:', error);
    }
  }, []);

  // Error haptic feedback
  const error = useCallback(() => {
    try {
      if (navigator.hapticFeedback) {
        navigator.hapticFeedback.notification('error');
        return;
      }

      if (navigator.vibrate) {
        navigator.vibrate([100, 50, 100, 50, 100]); // Pattern for error
      }
    } catch (error) {
      console.debug('Haptic feedback not available:', error);
    }
  }, []);

  // Selection haptic feedback for UI selections
  const selection = useCallback(() => {
    try {
      if (navigator.hapticFeedback) {
        navigator.hapticFeedback.selection();
        return;
      }

      if (navigator.vibrate) {
        navigator.vibrate(15); // Very light for selections
      }
    } catch (error) {
      console.debug('Haptic feedback not available:', error);
    }
  }, []);

  return {
    supportsHaptics,
    light,
    medium,
    heavy,
    success,
    warning,
    error,
    selection,
  };
};

/**
 * Hook for enhanced touch interactions with haptic feedback
 */
export const useTouchInteraction = () => {
  const haptic = useHapticFeedback();

  const handleTouchStart = useCallback((callback) => {
    return (e) => {
      haptic.light();
      if (callback) callback(e);
    };
  }, [haptic]);

  const handleTouchEnd = useCallback((callback) => {
    return (e) => {
      if (callback) callback(e);
    };
  }, []);

  const handlePress = useCallback((callback) => {
    return (e) => {
      haptic.medium();
      if (callback) callback(e);
    };
  }, [haptic]);

  const handleLongPress = useCallback((callback) => {
    return (e) => {
      haptic.heavy();
      if (callback) callback(e);
    };
  }, [haptic]);

  const handleSwipe = useCallback((direction, callback) => {
    return (e) => {
      haptic.light();
      if (callback) callback(e, direction);
    };
  }, [haptic]);

  return {
    handleTouchStart,
    handleTouchEnd,
    handlePress,
    handleLongPress,
    handleSwipe,
    haptic,
  };
};

export default useHapticFeedback;
