import React, { useState, useRef } from "react";
import styled from "styled-components";
import { motion, AnimatePresence } from "framer-motion";
import {
  Upload,
  X,
  File,
  FileText,
  Image,
  Download,
  AlertCircle,
} from "lucide-react";

const UploadContainer = styled.div`
  width: 100%;
`;

const DropZone = styled(motion.div)`
  border: 2px dashed
    ${({ theme, isDragOver, hasError }) =>
      hasError
        ? theme.colors.error
        : isDragOver
        ? theme.colors.primary
        : theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing[6]};
  text-align: center;
  background: ${({ theme, isDragOver }) =>
    isDragOver ? `${theme.colors.primary}10` : theme.colors.background};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: ${({ theme }) => theme.colors.primary};
    background: ${({ theme }) => `${theme.colors.primary}05`};
  }
`;

const UploadIcon = styled(Upload)`
  margin: 0 auto ${({ theme }) => theme.spacing[2]};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const UploadText = styled.p`
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: 0;
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const FileList = styled.div`
  margin-top: ${({ theme }) => theme.spacing[4]};
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const FileItem = styled(motion.div)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[3]};
  background: ${({ theme }) => theme.colors.surface};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.md};
`;

const FileIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  background: ${({ theme }) => `${theme.colors.primary}15`};
  color: ${({ theme }) => theme.colors.primary};
`;

const FileInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const FileName = styled.p`
  margin: 0;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const FileSize = styled.p`
  margin: 0;
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const FileActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  background: ${({ theme, variant }) =>
    variant === "danger"
      ? `${theme.colors.error}15`
      : `${theme.colors.primary}15`};
  color: ${({ theme, variant }) =>
    variant === "danger" ? theme.colors.error : theme.colors.primary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme, variant }) =>
      variant === "danger"
        ? `${theme.colors.error}25`
        : `${theme.colors.primary}25`};
  }
`;

const ErrorMessage = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  margin-top: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[3]};
  background: ${({ theme }) => `${theme.colors.error}15`};
  border: 1px solid ${({ theme }) => `${theme.colors.error}30`};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  color: ${({ theme }) => theme.colors.error};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const HiddenInput = styled.input`
  display: none;
`;

const getFileIcon = (type) => {
  if (type.startsWith("image/")) return Image;
  if (type.includes("pdf")) return FileText;
  return File;
};

const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const FileUpload = ({
  files = [],
  onChange,
  maxFiles = 10,
  maxSize = 5 * 1024 * 1024, // 5MB
  acceptedTypes = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "text/plain",
    "image/jpeg",
    "image/png",
    "image/gif",
  ],
  disabled = false,
  isUploading = false,
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [error, setError] = useState("");
  const fileInputRef = useRef(null);

  const validateFile = (file) => {
    // File size validation
    if (file.size > maxSize) {
      return `File "${
        file.name
      }" is too large. Maximum size is ${formatFileSize(maxSize)}.`;
    }

    // Empty file validation
    if (file.size === 0) {
      return `File "${file.name}" is empty and cannot be uploaded.`;
    }

    // File type validation
    if (!acceptedTypes.includes(file.type)) {
      return `File type "${file.type}" is not allowed.`;
    }

    // Filename validation
    if (!file.name || file.name.trim() === "") {
      return "Invalid filename.";
    }

    // Check for dangerous file extensions
    const dangerousExtensions = [
      ".exe",
      ".bat",
      ".cmd",
      ".com",
      ".pif",
      ".scr",
      ".vbs",
      ".js",
      ".jar",
      ".php",
      ".asp",
      ".aspx",
      ".jsp",
      ".sh",
      ".ps1",
      ".py",
      ".rb",
      ".pl",
    ];

    const fileExtension = file.name.toLowerCase().split(".").pop();
    if (dangerousExtensions.includes(`.${fileExtension}`)) {
      return `File type ".${fileExtension}" is not allowed for security reasons.`;
    }

    // Check for suspicious filename patterns
    if (
      file.name.includes("..") ||
      file.name.includes("/") ||
      file.name.includes("\\")
    ) {
      return "Invalid filename contains illegal characters.";
    }

    // Check for null bytes
    if (file.name.includes("\0")) {
      return "Invalid filename contains null bytes.";
    }

    // Check filename length
    if (file.name.length > 255) {
      return "Filename is too long. Maximum 255 characters allowed.";
    }

    // Check for double extensions
    const extensions = file.name.toLowerCase().match(/\.[a-z0-9]+/g) || [];
    if (extensions.length > 1) {
      const hasDangerousExtension = extensions.some((ext) =>
        dangerousExtensions.includes(ext)
      );
      if (hasDangerousExtension) {
        return "Files with multiple extensions are not allowed for security reasons.";
      }
    }

    return null;
  };

  const handleFiles = (newFiles) => {
    setError("");

    if (!newFiles || newFiles.length === 0) {
      return;
    }

    const fileArray = Array.from(newFiles);
    const totalFiles = files.length + fileArray.length;

    if (totalFiles > maxFiles) {
      setError(`Maximum ${maxFiles} files allowed.`);
      return;
    }

    const validFiles = [];
    let duplicateCount = 0;

    for (const file of fileArray) {
      const validationError = validateFile(file);
      if (validationError) {
        setError(validationError);
        return;
      }

      // Check for duplicates
      const isDuplicate = files.some(
        (existingFile) =>
          existingFile.name === file.name && existingFile.size === file.size
      );

      if (isDuplicate) {
        duplicateCount++;
      } else {
        validFiles.push(file);
      }
    }

    if (validFiles.length > 0) {
      onChange([...files, ...validFiles]);
    }

    if (duplicateCount > 0) {
      setError(`${duplicateCount} duplicate file(s) were skipped.`);
      // Clear error after 3 seconds for duplicate warning
      setTimeout(() => setError(""), 3000);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);

    if (disabled) return;

    const droppedFiles = e.dataTransfer.files;
    handleFiles(droppedFiles);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleClick = () => {
    if (!disabled) {
      fileInputRef.current?.click();
    }
  };

  const handleFileInput = (e) => {
    const selectedFiles = e.target.files;
    if (selectedFiles) {
      handleFiles(selectedFiles);
    }
    // Reset input value to allow selecting the same file again
    e.target.value = "";
  };

  const removeFile = (index) => {
    const newFiles = files.filter((_, i) => i !== index);
    onChange(newFiles);
    setError("");
  };

  const downloadFile = (file) => {
    if (file.url) {
      // For existing files with URLs
      window.open(file.url, "_blank");
    } else {
      // For newly selected files
      const url = URL.createObjectURL(file);
      const a = document.createElement("a");
      a.href = url;
      a.download = file.name;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  return (
    <UploadContainer>
      <DropZone
        isDragOver={isDragOver}
        hasError={!!error}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={handleClick}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2 }}
      >
        <UploadIcon size={32} />
        <UploadText>
          {disabled
            ? "File upload disabled"
            : isUploading
            ? "Uploading files..."
            : "Drop files here or click to browse"}
        </UploadText>
        <UploadText>
          Maximum {maxFiles} files, {formatFileSize(maxSize)} each
        </UploadText>
      </DropZone>

      <HiddenInput
        ref={fileInputRef}
        type="file"
        multiple
        accept={acceptedTypes.join(",")}
        onChange={handleFileInput}
        disabled={disabled}
      />

      {error && (
        <ErrorMessage>
          <AlertCircle size={16} />
          {error}
        </ErrorMessage>
      )}

      {files.length > 0 && (
        <FileList>
          <AnimatePresence>
            {files.map((file, index) => {
              const IconComponent = getFileIcon(file.type || "");
              return (
                <FileItem
                  key={`${file.name}-${index}`}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.2 }}
                >
                  <FileIcon>
                    <IconComponent size={16} />
                  </FileIcon>
                  <FileInfo>
                    <FileName>{file.name}</FileName>
                    <FileSize>{formatFileSize(file.size)}</FileSize>
                  </FileInfo>
                  <FileActions>
                    <ActionButton
                      type="button"
                      onClick={() => downloadFile(file)}
                      title="Download file"
                    >
                      <Download size={14} />
                    </ActionButton>
                    <ActionButton
                      type="button"
                      variant="danger"
                      onClick={() => removeFile(index)}
                      title="Remove file"
                    >
                      <X size={14} />
                    </ActionButton>
                  </FileActions>
                </FileItem>
              );
            })}
          </AnimatePresence>
        </FileList>
      )}
    </UploadContainer>
  );
};

export default FileUpload;
