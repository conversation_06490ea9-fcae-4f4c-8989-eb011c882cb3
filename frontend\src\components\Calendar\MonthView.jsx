import React from "react";
import styled from "styled-components";
import { motion } from "framer-motion";
import {
  formatDate,
  getDaysInMonth,
  getFirstDayOfMonth,
  isToday,
  isPast,
} from "../../utils/dateUtils";
import { EVENT_CATEGORIES } from "../../utils/constants";

const MonthContainer = styled.div`
  padding: ${({ theme }) => theme.spacing[4]};

  /* Desktop web view - spacious month container */
  @media (min-width: 769px) {
    padding: ${({ theme }) => theme.spacing[6]};
  }

  /* Mobile native feel - compact month container */
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing[2]};
  }
`;

const WeekHeader = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: ${({ theme }) => theme.colors.border.light};
  border-radius: ${({ theme }) => theme.borderRadius.md}
    ${({ theme }) => theme.borderRadius.md} 0 0;
  overflow: hidden;
`;

const WeekHeaderCell = styled.div`
  background: ${({ theme }) => theme.colors.background.secondary};
  padding: ${({ theme }) => theme.spacing[3]};
  text-align: center;
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};

  text-transform: uppercase;
  letter-spacing: 0.5px;

  /* Desktop web view - larger header cells */
  @media (min-width: 769px) {
    padding: ${({ theme }) => theme.spacing[4]};
    font-size: ${({ theme }) => theme.fontSizes.base};
    letter-spacing: 1px;
  }

  /* Mobile native feel - compact header cells */
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing[2]};
    font-size: ${({ theme }) => theme.fontSizes.xs};
    letter-spacing: 0.25px;
  }
`;

const MonthGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: ${({ theme }) => theme.colors.border.light};
  border-radius: 0 0 ${({ theme }) => theme.borderRadius.md}
    ${({ theme }) => theme.borderRadius.md};
  overflow: hidden;
`;

const DayCell = styled(motion.div)`
  background: ${({ theme, isOtherMonth, isToday }) => {
    if (isToday) return theme.colors.primary[50];
    if (isOtherMonth) return theme.colors.background.tertiary;
    return theme.colors.background.primary;
  }};
  min-height: 120px;
  padding: ${({ theme }) => theme.spacing[2]};
  cursor: pointer;
  border: ${({ theme, isToday }) =>
    isToday ? `2px solid ${theme.colors.primary[500]}` : "none"};
  transition: all ${({ theme }) => theme.transitions.fast};
  position: relative;

  &:hover {
    background: ${({ theme, isOtherMonth }) =>
      isOtherMonth
        ? theme.colors.background.tertiary
        : theme.colors.primary[25]};
  }

  /* Desktop web view - larger day cells with hover effects */
  @media (min-width: 769px) {
    min-height: 140px;
    padding: ${({ theme }) => theme.spacing[3]};

    &:hover {
      transform: translateY(-1px);
      box-shadow: ${({ theme }) => theme.shadows.sm};
    }
  }

  /* Mobile native feel - compact day cells with touch optimization */
  @media (max-width: 768px) {
    min-height: 70px;
    padding: ${({ theme }) => theme.spacing[1]};
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      transform: none; /* Disable hover effects on mobile */
    }

    &:active {
      background: ${({ theme, isOtherMonth }) =>
        isOtherMonth
          ? theme.colors.background.tertiary
          : theme.colors.primary[100]};
      transform: scale(0.98);
    }
  }
`;

const DayNumber = styled.div`
  font-weight: ${({ theme, isToday }) =>
    isToday ? theme.fontWeights.bold : theme.fontWeights.medium};
  color: ${({ theme, isOtherMonth, isPast, isToday }) => {
    if (isToday) return theme.colors.primary[700];
    if (isOtherMonth) return theme.colors.text.tertiary;
    if (isPast) return theme.colors.text.secondary;
    return theme.colors.text.primary;
  }};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  font-size: ${({ theme }) => theme.fontSizes.sm};

  /* Desktop web view - larger day numbers */
  @media (min-width: 769px) {
    font-size: ${({ theme }) => theme.fontSizes.lg};
    margin-bottom: ${({ theme }) => theme.spacing[3]};
  }

  /* Mobile native feel - compact day numbers */
  @media (max-width: 768px) {
    font-size: ${({ theme }) => theme.fontSizes.xs};
    margin-bottom: ${({ theme }) => theme.spacing[1]};
    line-height: 1.2;
  }
`;

const EventsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[1]};

  /* Desktop web view - more space for events */
  @media (min-width: 769px) {
    gap: ${({ theme }) => theme.spacing[2]};
  }

  /* Mobile native feel - compact event spacing */
  @media (max-width: 768px) {
    gap: 2px;
  }
`;

const EventItem = styled(motion.div)`
  background: ${({ theme, category }) => {
    const categoryData = EVENT_CATEGORIES.find((cat) => cat.value === category);
    return categoryData ? theme.colors.primary[100] : theme.colors.gray[100];
  }};
  color: ${({ theme, category }) => {
    const categoryData = EVENT_CATEGORIES.find((cat) => cat.value === category);
    return categoryData ? theme.colors.primary[800] : theme.colors.gray[800];
  }};
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &:hover {
    transform: translateY(-1px);
    box-shadow: ${({ theme }) => theme.shadows.sm};
  }

  /* Desktop web view - larger event items with hover effects */
  @media (min-width: 769px) {
    padding: ${({ theme }) => theme.spacing[2]}
      ${({ theme }) => theme.spacing[3]};
    font-size: ${({ theme }) => theme.fontSizes.sm};
    border-radius: ${({ theme }) => theme.borderRadius.md};

    &:hover {
      transform: translateY(-2px);
      box-shadow: ${({ theme }) => theme.shadows.md};
    }
  }

  /* Mobile native feel - compact event items with touch optimization */
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing[1]};
    font-size: 10px;
    border-radius: 2px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      transform: none; /* Disable hover effects on mobile */
    }

    &:active {
      transform: scale(0.98);
      opacity: 0.8;
    }
  }
`;

const MoreEvents = styled.div`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  cursor: pointer;
  padding: ${({ theme }) => theme.spacing[1]};

  &:hover {
    color: ${({ theme }) => theme.colors.primary[600]};
  }
`;

const MonthView = ({ currentDate, events, onEventClick, onDateClick }) => {
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  const daysInMonth = getDaysInMonth(year, month);
  const firstDayOfMonth = getFirstDayOfMonth(year, month);

  // Get previous month's last days
  const prevMonth = month === 0 ? 11 : month - 1;
  const prevYear = month === 0 ? year - 1 : year;
  const daysInPrevMonth = getDaysInMonth(prevYear, prevMonth);

  const weekDays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  // Create calendar grid
  const calendarDays = [];

  // Previous month's days
  for (let i = firstDayOfMonth - 1; i >= 0; i--) {
    const day = daysInPrevMonth - i;
    const date = new Date(prevYear, prevMonth, day);
    calendarDays.push({
      date,
      day,
      isOtherMonth: true,
      isToday: isToday(date),
      isPast: isPast(date),
    });
  }

  // Current month's days
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month, day);
    calendarDays.push({
      date,
      day,
      isOtherMonth: false,
      isToday: isToday(date),
      isPast: isPast(date),
    });
  }

  // Next month's days to fill the grid
  const remainingCells = 42 - calendarDays.length; // 6 weeks * 7 days
  const nextMonth = month === 11 ? 0 : month + 1;
  const nextYear = month === 11 ? year + 1 : year;

  for (let day = 1; day <= remainingCells; day++) {
    const date = new Date(nextYear, nextMonth, day);
    calendarDays.push({
      date,
      day,
      isOtherMonth: true,
      isToday: isToday(date),
      isPast: isPast(date),
    });
  }

  // Group events by date
  const eventsByDate = events.reduce((acc, event) => {
    const eventDate = formatDate(event.startDate, "yyyy-MM-dd");
    if (!acc[eventDate]) {
      acc[eventDate] = [];
    }
    acc[eventDate].push(event);
    return acc;
  }, {});

  const handleDayClick = (dayData) => {
    if (onDateClick) {
      onDateClick(dayData.date);
    }
  };

  const handleEventClick = (event, e) => {
    e.stopPropagation();
    if (onEventClick) {
      onEventClick(event);
    }
  };

  return (
    <MonthContainer>
      <WeekHeader>
        {weekDays.map((day) => (
          <WeekHeaderCell key={day}>{day}</WeekHeaderCell>
        ))}
      </WeekHeader>

      <MonthGrid>
        {calendarDays.map((dayData, index) => {
          const dateKey = formatDate(dayData.date, "yyyy-MM-dd");
          const dayEvents = eventsByDate[dateKey] || [];
          const maxVisibleEvents = 3;
          const visibleEvents = dayEvents.slice(0, maxVisibleEvents);
          const hiddenEventsCount = dayEvents.length - maxVisibleEvents;

          return (
            <DayCell
              key={`${dayData.date.getTime()}-${index}`}
              isOtherMonth={dayData.isOtherMonth}
              isToday={dayData.isToday}
              isPast={dayData.isPast}
              onClick={() => handleDayClick(dayData)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <DayNumber
                isOtherMonth={dayData.isOtherMonth}
                isToday={dayData.isToday}
                isPast={dayData.isPast}
              >
                {dayData.day}
              </DayNumber>

              <EventsContainer>
                {visibleEvents.map((event) => (
                  <EventItem
                    key={event._id}
                    category={event.category}
                    onClick={(e) => handleEventClick(event, e)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    title={event.title}
                  >
                    {event.title}
                  </EventItem>
                ))}

                {hiddenEventsCount > 0 && (
                  <MoreEvents onClick={() => handleDayClick(dayData)}>
                    +{hiddenEventsCount} more
                  </MoreEvents>
                )}
              </EventsContainer>
            </DayCell>
          );
        })}
      </MonthGrid>
    </MonthContainer>
  );
};

export default MonthView;
