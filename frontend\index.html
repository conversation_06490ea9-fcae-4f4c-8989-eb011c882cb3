<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />

    <!-- Favicon and Icons -->
    <link rel="icon" type="image/svg+xml" href="/icons/icon.svg" />
    <link rel="apple-touch-icon" href="/icons/apple-touch-icon.png" />
    <link rel="mask-icon" href="/icons/icon.svg" color="#2563eb" />

    <!-- Mobile Viewport and PWA Meta Tags -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=5.0, minimum-scale=1.0, user-scalable=yes, viewport-fit=cover"
    />
    <meta
      name="description"
      content="Grace Bible Fellowship Church Event Management System - Manage events, view calendar, and stay connected with your church community"
    />
    <meta name="theme-color" content="#2563eb" />
    <meta name="background-color" content="#ffffff" />

    <!-- PWA Meta Tags -->
    <meta name="application-name" content="GBF Events" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="GBF Events" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="msapplication-config" content="/browserconfig.xml" />
    <meta name="msapplication-TileColor" content="#2563eb" />
    <meta name="msapplication-tap-highlight" content="no" />

    <!-- Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <title>Grace Bible Fellowship</title>

    <!-- Basic styles to prevent flash of unstyled content -->
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      html,
      body {
        height: 100%;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
          "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans",
          "Helvetica Neue", sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #ffffff;
        -webkit-text-size-adjust: 100%;
        -webkit-tap-highlight-color: transparent;
        touch-action: manipulation;
        overflow-x: hidden;
        padding: env(safe-area-inset-top) env(safe-area-inset-right)
          env(safe-area-inset-bottom) env(safe-area-inset-left);
      }

      #root {
        height: 100%;
        min-height: 100vh;
      }

      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        flex-direction: column;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .loading-text {
        margin-top: 20px;
        color: #666;
        font-size: 16px;
      }

      .react-loaded .loading-container {
        display: none;
      }

      button,
      .button,
      [role="button"] {
        min-height: 44px;
        min-width: 44px;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
      }

      * {
        -webkit-overflow-scrolling: touch;
      }

      @media (display-mode: standalone) {
        body {
          padding-top: max(env(safe-area-inset-top), 20px);
        }
        .browser-only {
          display: none;
        }
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading Grace Bible Fellowship...</div>
      </div>
    </div>

    <script>
      window.addEventListener("error", function (e) {
        console.error("Global error:", e.error);
        const root = document.getElementById("root");
        if (root && !root.classList.contains("react-loaded")) {
          root.innerHTML = `
            <div style="padding: 20px; text-align: center; font-family: Arial, sans-serif;">
              <h2 style="color: #d63031; margin-bottom: 16px;">Application Error</h2>
              <p style="margin-bottom: 16px; color: #636e72;">
                Failed to load the application. Please try refreshing the page.
              </p>
              <button onclick="window.location.reload()" 
                      style="padding: 10px 20px; background: #0984e3; color: white; border: none; border-radius: 4px; cursor: pointer;">
                Refresh Page
              </button>
            </div>
          `;
        }
      });

      window.addEventListener("DOMContentLoaded", function () {
        setTimeout(function () {
          const root = document.getElementById("root");
          if (root && root.children.length > 1) {
            document.body.classList.add("react-loaded");
          }
        }, 1000);
      });
    </script>

    <!-- PWA Debug Script (Remove in production) -->
    <script>
      // Check PWA installability
      window.addEventListener("load", () => {
        console.log("🔍 PWA Debug Info:");
        console.log(
          "- HTTPS/Localhost:",
          window.location.protocol === "https:" ||
            window.location.hostname === "localhost"
        );
        console.log("- Service Worker Support:", "serviceWorker" in navigator);
        console.log(
          "- Manifest Link:",
          !!document.querySelector('link[rel="manifest"]')
        );
        console.log("- Secure Context:", window.isSecureContext);

        // Check manifest
        fetch("/manifest.json")
          .then((r) => r.json())
          .then((manifest) => {
            console.log("- Manifest loaded:", manifest.name);
            console.log("- Icons count:", manifest.icons?.length || 0);
          })
          .catch((e) => console.error("- Manifest error:", e));

        // Listen for beforeinstallprompt
        window.addEventListener("beforeinstallprompt", (e) => {
          console.log("🎉 PWA Install prompt available!");
          console.log("- Browser supports PWA installation");
        });

        // Check after 5 seconds
        setTimeout(() => {
          if (!window.deferredPrompt) {
            console.log("❌ No install prompt after 5 seconds. Check:");
            console.log("1. Are you using Chrome/Edge?");
            console.log("2. Is this HTTPS or localhost?");
            console.log("3. Are manifest and service worker working?");
            console.log("4. Try opening DevTools > Application > Manifest");
          }
        }, 5000);
      });
    </script>

    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
