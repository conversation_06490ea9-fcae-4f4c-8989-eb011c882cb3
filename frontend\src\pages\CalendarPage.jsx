import React, { useState } from "react";
import styled from "styled-components";
import { motion } from "framer-motion";
import { Plus, Download } from "lucide-react";
import CalendarView from "../components/Calendar/CalendarView";
import EventModal from "../components/Events/EventModal";
import EventForm from "../components/Events/EventForm";
import ExportModal from "../components/Export/ExportModal";
import { useEventsStore } from "../store";
import { useAuth } from "../hooks/useAuth";

const CalendarContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing[4]};

  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing[2]};
  }
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing[3]};

  @media (max-width: 768px) {
    display: none; /* Hide completely on mobile to avoid double headers */
  }
`;

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes["2xl"]};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.primary[700]};
  margin: 0;
  letter-spacing: -0.025em;

  /* Subtle spiritual emphasis */
  position: relative;
  &::before {
    content: "📅";
    position: absolute;
    left: -1.5rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.8em;
    opacity: 0.7;
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    font-size: ${({ theme }) => theme.fontSizes.xl};
    &::before {
      left: -1.2rem;
      font-size: 0.7em;
    }
  }
`;

const CreateButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[4]};
  background: ${({ theme }) => theme.colors.primary[600]};
  color: white;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  transition: all ${({ theme }) => theme.transitions.fast};

  &:hover {
    background: ${({ theme }) => theme.colors.primary[700]};
    transform: translateY(-1px);
    box-shadow: ${({ theme }) => theme.shadows.md};
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    padding: ${({ theme }) => theme.spacing[2]}
      ${({ theme }) => theme.spacing[3]};
    font-size: ${({ theme }) => theme.fontSizes.sm};
  }
`;

const CalendarWrapper = styled(motion.div)`
  background: ${({ theme }) => theme.colors.background.primary};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  overflow: hidden;
`;

const CreateEventButton = styled.button`
  position: fixed;
  bottom: ${({ theme }) => theme.spacing[6]};
  right: ${({ theme }) => theme.spacing[6]};
  width: 56px;
  height: 56px;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  background: ${({ theme }) => theme.colors.primary[600]};
  color: white;
  box-shadow: ${({ theme }) => theme.shadows.lg};
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all ${({ theme }) => theme.transitions.fast};
  z-index: ${({ theme }) => theme.zIndex[30]};

  &:hover {
    transform: scale(1.1);
    background: ${({ theme }) => theme.colors.primary[700]};
    box-shadow: ${({ theme }) => theme.shadows.xl};
  }

  @media (min-width: ${({ theme }) => theme.breakpoints.md}) {
    display: none;
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    bottom: ${({ theme }) => theme.spacing[4]};
    right: ${({ theme }) => theme.spacing[4]};
  }
`;

const CalendarPage = () => {
  const {
    createEvent,
    updateEvent,
    deleteEvent,
    isLoading,
    fetchCalendarEvents,
    exportEvents,
  } = useEventsStore();
  const { isAuthenticated, hasPermission } = useAuth();
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [showEventModal, setShowEventModal] = useState(false);
  const [showEventForm, setShowEventForm] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [editingEvent, setEditingEvent] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [isExporting, setIsExporting] = useState(false);

  const handleEventClick = (event) => {
    setSelectedEvent(event);
    setShowEventModal(true);
  };

  const handleDateClick = (date) => {
    setSelectedDate(date);
    if (isAuthenticated && hasPermission("canCreateEvents")) {
      setEditingEvent(null);
      setShowEventForm(true);
    }
  };

  const handleCreateEvent = () => {
    setEditingEvent(null);
    setSelectedDate(null);
    setShowEventForm(true);
  };

  const handleEditEvent = (event) => {
    setEditingEvent(event);
    setShowEventForm(true);
    setShowEventModal(false);
  };

  const handleDeleteEvent = async (event) => {
    if (window.confirm("Are you sure you want to delete this event?")) {
      try {
        await deleteEvent(event._id);

        // Refresh calendar events to show changes immediately
        const currentDate = new Date();
        const year = currentDate.getFullYear();
        const month = currentDate.getMonth() + 1;
        await fetchCalendarEvents(year, month);

        setShowEventModal(false);
      } catch (error) {
        console.error("Failed to delete event:", error);
        alert("Failed to delete event. Please try again.");
      }
    }
  };

  const handleFormSubmit = async (formData) => {
    try {
      if (editingEvent) {
        await updateEvent(editingEvent._id, formData);
      } else {
        await createEvent(formData);
      }

      // Refresh calendar events to show changes immediately
      const currentDate = new Date();
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;
      await fetchCalendarEvents(year, month);

      setShowEventForm(false);
      setEditingEvent(null);
      setSelectedDate(null);
    } catch (error) {
      console.error("Failed to save event:", error);
      alert("Failed to save event. Please try again.");
    }
  };

  const handleExport = async (format) => {
    try {
      setIsExporting(true);
      const response = await exportEvents({ format });

      if (response && response.data) {
        let blob, fileExtension;

        console.log("Calendar export response received:", {
          format,
          dataType: typeof response.data,
          dataSize: response.data.byteLength || response.data.length,
          isArrayBuffer: response.data instanceof ArrayBuffer,
        });

        switch (format) {
          case "pdf":
            // For PDF, response.data is ArrayBuffer when responseType is 'arraybuffer'
            blob = new Blob([response.data], { type: "application/pdf" });
            fileExtension = "pdf";
            break;
          case "word":
          case "docx":
            // For Word, response.data is ArrayBuffer when responseType is 'arraybuffer'
            blob = new Blob([response.data], {
              type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            });
            fileExtension = "docx";
            break;
          case "csv":
          default:
            // For CSV, response.data is text when responseType is 'text'
            blob = new Blob([response.data], {
              type: "text/csv;charset=utf-8;",
            });
            fileExtension = "csv";
            break;
        }
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);

        const currentDate = new Date().toISOString().split("T")[0];
        const filename = `grace-bible-fellowship-calendar-${currentDate}.${fileExtension}`;

        link.setAttribute("download", filename);
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert(`Calendar exported successfully! Downloaded as ${filename}`);
        setShowExportModal(false);
      }
    } catch (error) {
      console.error("Failed to export calendar:", error);
      alert("Failed to export calendar. Please try again.");
    } finally {
      setIsExporting(false);
    }
  };

  const handleExportClick = () => {
    setShowExportModal(true);
  };

  const canCreateEvents = isAuthenticated && hasPermission("canCreateEvents");
  const canExportData = isAuthenticated && hasPermission("canExportData");

  return (
    <CalendarContainer>
      <PageHeader>
        <PageTitle>Church Calendar</PageTitle>
        <div style={{ display: "flex", gap: "12px" }}>
          {canExportData && (
            <CreateButton
              onClick={handleExportClick}
              style={{ backgroundColor: "#6b7280" }}
            >
              <Download size={20} />
              <span className="hidden-mobile">Export</span>
            </CreateButton>
          )}
          {canCreateEvents && (
            <CreateButton onClick={handleCreateEvent}>
              <Plus size={20} />
              <span className="hidden-mobile">Create Event</span>
            </CreateButton>
          )}
        </div>
      </PageHeader>

      <CalendarWrapper
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <CalendarView
          onEventClick={handleEventClick}
          onDateClick={handleDateClick}
        />
      </CalendarWrapper>

      {canCreateEvents && (
        <CreateEventButton onClick={handleCreateEvent} title="Create New Event">
          <Plus size={24} />
        </CreateEventButton>
      )}

      <EventModal
        event={selectedEvent}
        isOpen={showEventModal}
        onClose={() => setShowEventModal(false)}
        onEdit={handleEditEvent}
        onDelete={handleDeleteEvent}
      />

      <EventForm
        event={editingEvent}
        selectedDate={selectedDate}
        isOpen={showEventForm}
        onClose={() => {
          setShowEventForm(false);
          setEditingEvent(null);
          setSelectedDate(null);
        }}
        onSubmit={handleFormSubmit}
        isLoading={isLoading}
      />

      <ExportModal
        isOpen={showExportModal}
        onClose={() => setShowExportModal(false)}
        onExport={handleExport}
        isLoading={isExporting}
      />
    </CalendarContainer>
  );
};

export default CalendarPage;
