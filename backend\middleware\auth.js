const jwt = require("jsonwebtoken");
const { User } = require("../models");
const { ActivityLog } = require("../models");

// Middleware to verify JWT token
const authenticate = async (req, res, next) => {
  try {
    const token = req.header("Authorization")?.replace("Bearer ", "");

    if (!token) {
      return res.status(401).json({
        success: false,
        message: "Access denied. No token provided.",
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.id).select("+password");

    if (!user) {
      return res.status(401).json({
        success: false,
        message: "Invalid token. User not found.",
      });
    }

    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: "Account is deactivated.",
      });
    }

    req.user = user;
    next();
  } catch (error) {
    if (error.name === "JsonWebTokenError") {
      return res.status(401).json({
        success: false,
        message: "Invalid token.",
      });
    }
    if (error.name === "TokenExpiredError") {
      return res.status(401).json({
        success: false,
        message: "Token expired.",
      });
    }

    console.error("Authentication error:", error);
    res.status(500).json({
      success: false,
      message: "Server error during authentication.",
    });
  }
};

// Middleware to check if user has required role
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: "Authentication required.",
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: "Access denied. Insufficient permissions.",
      });
    }

    next();
  };
};

// Middleware to check specific permissions
const checkPermission = (permission) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: "Authentication required.",
      });
    }

    if (!req.user.permissions[permission]) {
      return res.status(403).json({
        success: false,
        message: `Access denied. Missing permission: ${permission}`,
      });
    }

    next();
  };
};

// Middleware for optional authentication (for public routes that can benefit from user context)
const optionalAuth = async (req, res, next) => {
  try {
    const token = req.header("Authorization")?.replace("Bearer ", "");

    if (token) {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.id);

      if (user && user.isActive) {
        req.user = user;
      }
    }

    next();
  } catch (error) {
    // For optional auth, we don't return errors, just continue without user
    next();
  }
};

// Middleware to log user activity
const logActivity = (action, resource) => {
  return async (req, res, next) => {
    try {
      const originalSend = res.send;

      res.send = function (data) {
        // Log activity after response is sent
        setImmediate(async () => {
          try {
            // Only log activities for authenticated users
            if (!req.user?._id) {
              return;
            }

            const activityData = {
              user: req.user._id,
              action,
              resource,
              details: {
                method: req.method,
                url: req.originalUrl,
                body:
                  action.includes("create") || action.includes("update")
                    ? req.body
                    : undefined,
              },
              ipAddress: req.ip || req.connection.remoteAddress,
              userAgent: req.get("User-Agent"),
              success: res.statusCode < 400,
            };

            // Only set resourceId if it's required for this action
            const resourceId = req.params.id || req.body._id;
            if (resourceId && resourceId !== "unknown") {
              activityData.resourceId = resourceId;
            }

            if (res.statusCode >= 400) {
              activityData.errorMessage =
                typeof data === "string" ? data : JSON.stringify(data);
            }

            await ActivityLog.logActivity(activityData);
          } catch (logError) {
            console.error("Error logging activity:", logError);
          }
        });

        originalSend.call(this, data);
      };

      next();
    } catch (error) {
      console.error("Error in activity logging middleware:", error);
      next();
    }
  };
};

// Middleware to validate admin access for user management
const adminOnly = (req, res, next) => {
  if (!req.user || req.user.role !== "admin") {
    return res.status(403).json({
      success: false,
      message: "Admin access required.",
    });
  }
  next();
};

// Middleware to check if user can modify resource (own resource or admin)
const canModifyResource = (req, res, next) => {
  const resourceUserId =
    req.params.userId || req.body.createdBy || req.body.organizer;

  if (req.user.role === "admin" || req.user._id.toString() === resourceUserId) {
    return next();
  }

  res.status(403).json({
    success: false,
    message: "You can only modify your own resources.",
  });
};

module.exports = {
  authenticate,
  authorize,
  checkPermission,
  optionalAuth,
  logActivity,
  adminOnly,
  canModifyResource,
};
