import React from "react";
import { createRoot } from "react-dom/client";
import App from "./App.jsx";
import ErrorBoundary from "./components/ErrorBoundary.jsx";

// Get the root element
const rootElement = document.getElementById("root");

if (!rootElement) {
  throw new Error(
    "Root element not found. Make sure there's a div with id='root' in your HTML."
  );
}

// Create root and render the app
const root = createRoot(rootElement);

// Render the application
try {
  root.render(
    <React.StrictMode>
      <ErrorBoundary>
        <App />
      </ErrorBoundary>
    </React.StrictMode>
  );

  // Mark that React has successfully loaded
  setTimeout(() => {
    document.body.classList.add("react-loaded");
  }, 100);
} catch (error) {
  console.error("Failed to render React app:", error);

  // Fallback error display
  rootElement.innerHTML = `
    <div style="padding: 20px; text-align: center; font-family: Arial, sans-serif;">
      <h2 style="color: #d63031; margin-bottom: 16px;">React Rendering Error</h2>
      <p style="margin-bottom: 16px; color: #636e72;">
        Failed to initialize the React application.
      </p>
      <button onclick="window.location.reload()"
              style="padding: 10px 20px; background: #0984e3; color: white; border: none; border-radius: 4px; cursor: pointer;">
        Refresh Page
      </button>
      <details style="margin-top: 20px; text-align: left;">
        <summary style="cursor: pointer;">Error Details</summary>
        <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow: auto; font-size: 12px;">
${error.stack || error.message}
        </pre>
      </details>
    </div>
  `;
}
