import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { useLocation } from "react-router-dom";
import Header from "./Header";
import Footer from "./Footer";
import MobileHeader from "./MobileHeader";
import BottomNavigation from "./BottomNavigation";
import { useStandalone } from "../../hooks/useTouch";

const LayoutContainer = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
`;

const Main = styled.main`
  flex: 1;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing[4]};

  /* Desktop styles */
  @media (min-width: ${({ theme }) => theme.breakpoints.md}) {
    padding: ${({ theme }) => theme.spacing[6]}
      ${({ theme }) => theme.spacing[4]};
  }

  /* Mobile styles */
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing[2]};
    padding-top: calc(
      56px + ${({ theme }) => theme.spacing[2]}
    ); /* Mobile header height + spacing */
    padding-bottom: calc(
      80px + ${({ theme }) => theme.spacing[2]}
    ); /* Bottom nav height + spacing */

    /* Adjust for standalone PWA */
    ${({ isStandalone, theme }) =>
      isStandalone &&
      `
      padding-top: calc(56px + env(safe-area-inset-top) + ${theme.spacing[2]});
      padding-bottom: calc(80px + env(safe-area-inset-bottom) + ${theme.spacing[2]});
    `}
  }
`;

const MobileOnly = styled.div`
  @media (min-width: 769px) {
    display: none;
  }
`;

const DesktopOnly = styled.div`
  @media (max-width: 768px) {
    display: none;
  }
`;

const Layout = ({ children }) => {
  const location = useLocation();
  const isStandalone = useStandalone();
  const [showEventModal, setShowEventModal] = useState(false);
  const [currentSearchTerm, setCurrentSearchTerm] = useState("");

  // Determine mobile header props based on current page
  const getMobileHeaderProps = () => {
    const path = location.pathname;

    if (path === "/events") {
      return {
        showSearch: true,
        showNotifications: true,
        onSearch: (query) => {
          console.log("Mobile search triggered:", query);
          setCurrentSearchTerm(query);
          // Dispatch custom event for EventsPage to listen to
          window.dispatchEvent(
            new CustomEvent("mobileSearch", {
              detail: { query },
            })
          );
        },
        currentSearchTerm,
      };
    }

    if (path === "/calendar") {
      return {
        showNotifications: true,
      };
    }

    return {};
  };

  const handleCreateEvent = () => {
    setShowEventModal(true);
  };

  const handleCloseEventModal = () => {
    setShowEventModal(false);
  };

  // Listen for search term updates from EventsPage
  useEffect(() => {
    const handleSearchTermUpdate = (event) => {
      setCurrentSearchTerm(event.detail.searchTerm);
    };

    window.addEventListener("searchTermUpdate", handleSearchTermUpdate);

    return () => {
      window.removeEventListener("searchTermUpdate", handleSearchTermUpdate);
    };
  }, []);

  // Clear search term when navigating away from events page
  useEffect(() => {
    if (location.pathname !== "/events") {
      setCurrentSearchTerm("");
    }
  }, [location.pathname]);

  return (
    <LayoutContainer>
      {/* Desktop Header */}
      <DesktopOnly>
        <Header />
      </DesktopOnly>

      {/* Mobile Header */}
      <MobileOnly>
        <MobileHeader
          {...getMobileHeaderProps()}
          onNotificationClick={() => {
            // Handle notifications
            console.log("Notifications clicked");
          }}
        />
      </MobileOnly>

      <Main isStandalone={isStandalone}>{children}</Main>

      {/* Desktop Footer */}
      <DesktopOnly>
        <Footer />
      </DesktopOnly>

      {/* Mobile Bottom Navigation */}
      <MobileOnly>
        <BottomNavigation onCreateEvent={handleCreateEvent} />
      </MobileOnly>

      {/* Event Modal would be handled by individual pages */}
    </LayoutContainer>
  );
};

export default Layout;
