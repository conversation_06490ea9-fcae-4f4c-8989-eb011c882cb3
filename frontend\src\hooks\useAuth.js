import { useEffect } from "react";
import { useAuthStore } from "../store";

export const useAuth = () => {
  const {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    updateProfile,
    changePassword,
    verifyToken,
    clearError,
    isAdmin,
    isEditor,
    hasPermission,
  } = useAuthStore();

  // Verify token on mount
  useEffect(() => {
    const token = localStorage.getItem("token");
    if (token && !isAuthenticated) {
      verifyToken();
    }
  }, [isAuthenticated, verifyToken]);

  return {
    // State
    user,
    token,
    isAuthenticated,
    isLoading,
    error,

    // Actions
    login,
    logout,
    updateProfile,
    changePassword,
    verifyToken,
    clearError,

    // Computed values
    isAdmin: isAdmin(),
    isEditor: isEditor(),
    hasPermission,

    // User info
    fullName: user ? `${user.firstName} ${user.lastName}` : "",
    role: user?.role || "",
    permissions: user?.permissions || {},
  };
};
