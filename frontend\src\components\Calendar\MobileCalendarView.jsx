import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { motion, AnimatePresence, PanInfo } from "framer-motion";
import {
  ChevronLeft,
  ChevronRight,
  Calendar as CalendarIcon,
  List,
  Grid3X3,
} from "lucide-react";
import { useTouchInteraction } from "../../hooks/useHapticFeedback";
import { useTouch } from "../../hooks/useTouch";

const CalendarContainer = styled.div`
  background: ${({ theme }) => theme.colors.background.primary};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  overflow: hidden;
  box-shadow: ${({ theme }) => theme.shadows.sm};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  margin: 0 auto;
  max-width: 100%;

  /* Optimize for mobile screens */
  @media (max-width: 768px) {
    border-radius: ${({ theme }) => theme.borderRadius.md};
    margin: 0;
    border-left: none;
    border-right: none;
    box-shadow: none;
  }
`;

const CalendarHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${({ theme }) => theme.spacing[3]};
  background: ${({ theme }) => theme.colors.primary[600]};
  color: white;
  min-height: 56px;
`;

const MonthNavigation = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  flex: 1;
`;

const NavButton = styled(motion.button)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;

  &:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.3);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const MonthTitle = styled(motion.h2)`
  font-size: ${({ theme }) => theme.fontSizes.base};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  margin: 0;
  flex: 1;
  text-align: center;
  color: white;
`;

const ViewToggle = styled.div`
  display: flex;
  background: rgba(255, 255, 255, 0.2);
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: 2px;
  gap: 2px;
`;

const ViewButton = styled(motion.button)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  border: none;
  background: ${({ isActive }) =>
    isActive ? "rgba(255, 255, 255, 0.3)" : "transparent"};
  color: white;
  cursor: pointer;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;

  &:focus {
    outline: none;
  }
`;

const CalendarGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: ${({ theme }) => theme.colors.background.primary};

  /* Optimize grid for mobile */
  @media (max-width: 768px) {
    gap: 0;
  }
`;

const WeekdayHeader = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: ${({ theme }) => theme.colors.background.secondary};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
`;

const WeekdayCell = styled.div`
  padding: ${({ theme }) => theme.spacing[2]};
  text-align: center;
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.text.secondary};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const DayCell = styled(motion.div)`
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing[1]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  border-right: 1px solid ${({ theme }) => theme.colors.border.light};
  cursor: pointer;
  position: relative;
  min-height: 44px; /* Minimum touch target size */
  background: ${({ theme, isToday, isSelected, isOtherMonth }) => {
    if (isSelected) return theme.colors.primary[100];
    if (isToday) return theme.colors.secondary[50];
    if (isOtherMonth) return theme.colors.background.tertiary;
    return theme.colors.background.primary;
  }};
  color: ${({ theme, isToday, isOtherMonth }) => {
    if (isOtherMonth) return theme.colors.text.muted;
    if (isToday) return theme.colors.primary[700];
    return theme.colors.text.primary;
  }};
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;

  &:nth-child(7n) {
    border-right: none;
  }

  &:hover {
    background: ${({ theme, isOtherMonth }) =>
      isOtherMonth
        ? theme.colors.background.tertiary
        : theme.colors.primary[50]};
  }

  /* Mobile optimizations */
  @media (max-width: 768px) {
    min-height: 40px;
    padding: ${({ theme }) => theme.spacing[1]} 2px;
  }
`;

const DayNumber = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme, isToday }) =>
    isToday ? theme.fontWeights.bold : theme.fontWeights.medium};
  margin-bottom: ${({ theme }) => theme.spacing[1]};
  line-height: 1;
`;

const EventDots = styled.div`
  display: flex;
  gap: 1px;
  flex-wrap: wrap;
  justify-content: center;
  max-width: 100%;
  margin-top: 2px;
`;

const EventDot = styled.div`
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: ${({ theme, color }) => color || theme.colors.primary[500]};
  flex-shrink: 0;
`;

const TodayIndicator = styled.div`
  position: absolute;
  top: 2px;
  right: 2px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: ${({ theme }) => theme.colors.primary[500]};
`;

const SwipeContainer = styled.div`
  position: relative;
  overflow: hidden;
`;

const MobileCalendarView = ({
  currentDate,
  events = [],
  onDateSelect,
  onMonthChange,
  selectedDate,
}) => {
  const [viewMode, setViewMode] = useState("month"); // 'month', 'week', 'list'
  const [calendarDate, setCalendarDate] = useState(currentDate || new Date());
  const { handlePress, haptic } = useTouchInteraction();

  const weekdays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
  const today = new Date();

  // Generate calendar days
  const generateCalendarDays = () => {
    const year = calendarDate.getFullYear();
    const month = calendarDate.getMonth();

    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days = [];
    const current = new Date(startDate);

    for (let i = 0; i < 42; i++) {
      // 6 weeks
      const dayEvents = events.filter((event) => {
        const eventDate = new Date(event.date);
        return eventDate.toDateString() === current.toDateString();
      });

      days.push({
        date: new Date(current),
        isCurrentMonth: current.getMonth() === month,
        isToday: current.toDateString() === today.toDateString(),
        isSelected:
          selectedDate &&
          current.toDateString() === selectedDate.toDateString(),
        events: dayEvents,
      });

      current.setDate(current.getDate() + 1);
    }

    return days;
  };

  const days = generateCalendarDays();

  const handlePrevMonth = () => {
    const newDate = new Date(calendarDate);
    newDate.setMonth(newDate.getMonth() - 1);
    setCalendarDate(newDate);
    haptic.light();
    if (onMonthChange) onMonthChange(newDate);
  };

  const handleNextMonth = () => {
    const newDate = new Date(calendarDate);
    newDate.setMonth(newDate.getMonth() + 1);
    setCalendarDate(newDate);
    haptic.light();
    if (onMonthChange) onMonthChange(newDate);
  };

  const handleDaySelect = (day) => {
    haptic.medium();
    if (onDateSelect) onDateSelect(day.date);
  };

  const handleSwipeLeft = () => {
    handleNextMonth();
  };

  const handleSwipeRight = () => {
    handlePrevMonth();
  };

  const { touchRef } = useTouch({
    onSwipeLeft: handleSwipeLeft,
    onSwipeRight: handleSwipeRight,
    threshold: 50,
  });

  const getEventColor = (event) => {
    // Map event categories to colors
    const categoryColors = {
      worship: "#7c3aed",
      fellowship: "#eab308",
      service: "#0066cc",
      prayer: "#a855f7",
      study: "#059669",
      youth: "#dc2626",
      children: "#ea580c",
      music: "#7c2d12",
    };

    return categoryColors[event.category] || "#0066cc";
  };

  const formatMonthYear = (date) => {
    return date.toLocaleDateString("en-US", {
      month: "long",
      year: "numeric",
    });
  };

  return (
    <CalendarContainer>
      <CalendarHeader>
        <MonthNavigation>
          <NavButton onClick={handlePrevMonth} whileTap={{ scale: 0.9 }}>
            <ChevronLeft size={20} />
          </NavButton>

          <MonthTitle
            key={formatMonthYear(calendarDate)}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.2 }}
          >
            {formatMonthYear(calendarDate)}
          </MonthTitle>

          <NavButton onClick={handleNextMonth} whileTap={{ scale: 0.9 }}>
            <ChevronRight size={20} />
          </NavButton>
        </MonthNavigation>

        <ViewToggle>
          <ViewButton
            isActive={viewMode === "month"}
            onClick={() => setViewMode("month")}
            whileTap={{ scale: 0.9 }}
          >
            <Grid3X3 size={16} />
          </ViewButton>
          <ViewButton
            isActive={viewMode === "list"}
            onClick={() => setViewMode("list")}
            whileTap={{ scale: 0.9 }}
          >
            <List size={16} />
          </ViewButton>
        </ViewToggle>
      </CalendarHeader>

      {viewMode === "month" && (
        <SwipeContainer ref={touchRef}>
          <WeekdayHeader>
            {weekdays.map((day) => (
              <WeekdayCell key={day}>{day}</WeekdayCell>
            ))}
          </WeekdayHeader>

          <CalendarGrid
            key={formatMonthYear(calendarDate)}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            {days.map((day, index) => (
              <DayCell
                key={index}
                isToday={day.isToday}
                isSelected={day.isSelected}
                isOtherMonth={!day.isCurrentMonth}
                onClick={() => handleDaySelect(day)}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.01, duration: 0.2 }}
              >
                {day.isToday && <TodayIndicator />}

                <DayNumber isToday={day.isToday}>
                  {day.date.getDate()}
                </DayNumber>

                {day.events.length > 0 && (
                  <EventDots>
                    {day.events.slice(0, 3).map((event, eventIndex) => (
                      <EventDot key={eventIndex} color={getEventColor(event)} />
                    ))}
                    {day.events.length > 3 && <EventDot color="#666" />}
                  </EventDots>
                )}
              </DayCell>
            ))}
          </CalendarGrid>
        </SwipeContainer>
      )}
    </CalendarContainer>
  );
};

export default MobileCalendarView;
