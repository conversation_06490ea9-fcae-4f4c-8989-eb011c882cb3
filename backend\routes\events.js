const express = require("express");
const path = require("path");
const { Event, ActivityLog } = require("../models");
const {
  authenticate,
  optionalAuth,
  checkPermission,
  logActivity,
  canModifyResource,
} = require("../middleware/auth");
const {
  eventValidation,
  mongoIdValidation,
  paginationValidation,
  handleValidationErrors,
} = require("../utils/validation");
const {
  upload,
  handleUploadError,
  deleteFile,
  getFileInfo,
  uploadDir,
} = require("../middleware/upload");
const { generatePDF, generateWord } = require("../utils/exportUtils");

const router = express.Router();

// Test endpoint for binary data
router.get("/test-pdf", async (req, res) => {
  try {
    console.log("Testing PDF generation...");
    const { generatePDF } = require("../utils/exportUtils");

    const testEvent = {
      title: "Test Event",
      startDate: new Date(),
      endDate: new Date(),
      startTime: "10:00",
      endTime: "11:00",
      description: "Test description",
      location: { name: "Test Location" },
      pointOfContact: { name: "Test Contact" },
    };

    const pdfBuffer = await generatePDF([testEvent]);

    console.log(`Test PDF generated. Size: ${pdfBuffer.length} bytes`);
    console.log(`First 10 bytes: ${pdfBuffer.slice(0, 10).toString("hex")}`);

    res.setHeader("Content-Type", "application/pdf");
    res.setHeader("Content-Disposition", 'attachment; filename="test.pdf"');
    res.setHeader("Content-Length", pdfBuffer.length);

    res.end(pdfBuffer, "binary");
  } catch (error) {
    console.error("Test PDF error:", error);
    res.status(500).json({ error: error.message });
  }
});

// Test endpoint to serve the pre-generated PDF file
router.get("/test-file-pdf", (req, res) => {
  try {
    const fs = require("fs");
    const path = require("path");
    const filePath = path.join(__dirname, "..", "test-clean.pdf");

    if (fs.existsSync(filePath)) {
      const fileBuffer = fs.readFileSync(filePath);
      console.log(`Serving test file. Size: ${fileBuffer.length} bytes`);

      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        'attachment; filename="test-file.pdf"'
      );
      res.setHeader("Content-Length", fileBuffer.length);

      res.end(fileBuffer, "binary");
    } else {
      res.status(404).json({ error: "Test file not found" });
    }
  } catch (error) {
    console.error("Test file error:", error);
    res.status(500).json({ error: error.message });
  }
});

// @route   GET /api/events
// @desc    Get all events (public route with optional auth)
// @access  Public
router.get(
  "/",
  optionalAuth,
  paginationValidation,
  handleValidationErrors,
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // Build query based on user authentication
      let query = {};

      if (!req.user) {
        // Public users can only see published public events
        query = { isPublic: true, status: "published" };
      } else {
        // Authenticated users can see their own events and public events
        query = {
          $or: [
            { isPublic: true, status: "published" },
            { createdBy: req.user._id },
          ],
        };
      }

      // Add filters
      if (req.query.category) {
        query.category = req.query.category;
      }

      if (req.query.startDate && req.query.endDate) {
        query.startDate = {
          $gte: new Date(req.query.startDate),
          $lte: new Date(req.query.endDate),
        };
      }

      if (req.query.search) {
        query.$text = { $search: req.query.search };
      }

      // Sort options
      let sort = { startDate: 1 };
      if (req.query.sort) {
        const sortField = req.query.sort.startsWith("-")
          ? req.query.sort.substring(1)
          : req.query.sort;
        const sortOrder = req.query.sort.startsWith("-") ? -1 : 1;
        sort = { [sortField]: sortOrder };
      }

      const events = await Event.find(query)
        .populate("organizer", "firstName lastName email")
        .populate("createdBy", "firstName lastName")
        .sort(sort)
        .skip(skip)
        .limit(limit);

      const total = await Event.countDocuments(query);

      res.json({
        success: true,
        data: {
          events,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit),
          },
        },
      });
    } catch (error) {
      console.error("Get events error:", error);
      res.status(500).json({
        success: false,
        message: "Server error while fetching events",
      });
    }
  }
);

// @route   GET /api/events/:id
// @desc    Get single event
// @access  Public
router.get(
  "/:id",
  mongoIdValidation,
  handleValidationErrors,
  optionalAuth,
  async (req, res) => {
    try {
      const event = await Event.findById(req.params.id)
        .populate("organizer", "firstName lastName email phone")
        .populate("createdBy", "firstName lastName")
        .populate("lastModifiedBy", "firstName lastName");

      if (!event) {
        return res.status(404).json({
          success: false,
          message: "Event not found",
        });
      }

      // Check if user can view this event
      if (
        !event.isPublic &&
        (!req.user ||
          req.user._id.toString() !== event.createdBy._id.toString())
      ) {
        return res.status(403).json({
          success: false,
          message: "Access denied. This is a private event.",
        });
      }

      res.json({
        success: true,
        data: {
          event,
        },
      });
    } catch (error) {
      console.error("Get event error:", error);
      res.status(500).json({
        success: false,
        message: "Server error while fetching event",
      });
    }
  }
);

// @route   POST /api/events
// @desc    Create new event
// @access  Private (Admin/Editor)
router.post(
  "/",
  authenticate,
  checkPermission("canCreateEvents"),
  eventValidation,
  handleValidationErrors,
  async (req, res) => {
    try {
      const eventData = {
        ...req.body,
        organizer: req.body.organizer || req.user._id,
        createdBy: req.user._id,
      };

      // Parse attachments if they're sent as a string
      if (typeof eventData.attachments === "string") {
        try {
          eventData.attachments = JSON.parse(eventData.attachments);
        } catch (parseError) {
          console.error("Failed to parse attachments:", parseError);
          eventData.attachments = [];
        }
      }

      const event = new Event(eventData);
      await event.save();

      await event.populate("organizer", "firstName lastName email");
      await event.populate("createdBy", "firstName lastName");

      // Log activity after event is created
      try {
        await ActivityLog.logActivity({
          user: req.user._id,
          action: "create_event",
          resource: "event",
          resourceId: event._id,
          details: {
            method: req.method,
            url: req.originalUrl,
            eventTitle: event.title,
          },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.get("User-Agent"),
          success: true,
        });
      } catch (logError) {
        console.error("Error logging create event activity:", logError);
      }

      res.status(201).json({
        success: true,
        message: "Event created successfully",
        data: {
          event,
        },
      });
    } catch (error) {
      console.error("Create event error:", error);

      if (error.name === "ValidationError") {
        return res.status(400).json({
          success: false,
          message: "Validation error",
          errors: Object.values(error.errors).map((err) => ({
            field: err.path,
            message: err.message,
          })),
        });
      }

      res.status(500).json({
        success: false,
        message: "Server error while creating event",
      });
    }
  }
);

// @route   PUT /api/events/:id
// @desc    Update event
// @access  Private (Admin/Editor - own events or admin)
router.put(
  "/:id",
  mongoIdValidation,
  handleValidationErrors,
  authenticate,
  checkPermission("canEditEvents"),
  eventValidation,
  handleValidationErrors,
  async (req, res) => {
    try {
      const event = await Event.findById(req.params.id);

      if (!event) {
        return res.status(404).json({
          success: false,
          message: "Event not found",
        });
      }

      // Check if user can modify this event
      if (
        req.user.role !== "admin" &&
        event.createdBy.toString() !== req.user._id.toString()
      ) {
        return res.status(403).json({
          success: false,
          message: "You can only edit your own events",
        });
      }

      // Parse attachments if they're sent as a string
      if (typeof req.body.attachments === "string") {
        try {
          req.body.attachments = JSON.parse(req.body.attachments);
        } catch (parseError) {
          console.error("Failed to parse attachments in update:", parseError);
          req.body.attachments = [];
        }
      }

      // Handle file cleanup for removed attachments
      if (req.body.attachments && Array.isArray(req.body.attachments)) {
        const oldAttachments = event.attachments || [];
        const newAttachments = req.body.attachments;

        // Find removed attachments
        const removedAttachments = oldAttachments.filter(
          (oldFile) =>
            !newAttachments.some(
              (newFile) => newFile.filename === oldFile.filename
            )
        );

        // Delete removed files from disk
        removedAttachments.forEach((attachment) => {
          if (attachment.filename) {
            const filePath = path.join(
              uploadDir,
              "events",
              attachment.filename
            );
            deleteFile(filePath);
          }
        });
      }

      // Update event
      Object.assign(event, req.body);
      event.lastModifiedBy = req.user._id;

      await event.save();

      await event.populate("organizer", "firstName lastName email");
      await event.populate("createdBy", "firstName lastName");
      await event.populate("lastModifiedBy", "firstName lastName");

      // Log activity after event is updated
      try {
        await ActivityLog.logActivity({
          user: req.user._id,
          action: "update_event",
          resource: "event",
          resourceId: event._id,
          details: {
            method: req.method,
            url: req.originalUrl,
            eventTitle: event.title,
          },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.get("User-Agent"),
          success: true,
        });
      } catch (logError) {
        console.error("Error logging update event activity:", logError);
      }

      res.json({
        success: true,
        message: "Event updated successfully",
        data: {
          event,
        },
      });
    } catch (error) {
      console.error("Update event error:", error);

      if (error.name === "ValidationError") {
        return res.status(400).json({
          success: false,
          message: "Validation error",
          errors: Object.values(error.errors).map((err) => ({
            field: err.path,
            message: err.message,
          })),
        });
      }

      res.status(500).json({
        success: false,
        message: "Server error while updating event",
      });
    }
  }
);

// @route   DELETE /api/events/:id
// @desc    Delete event
// @access  Private (Admin/Editor - own events or admin)
router.delete(
  "/:id",
  mongoIdValidation,
  handleValidationErrors,
  authenticate,
  checkPermission("canDeleteEvents"),
  async (req, res) => {
    try {
      const event = await Event.findById(req.params.id);

      if (!event) {
        return res.status(404).json({
          success: false,
          message: "Event not found",
        });
      }

      // Check if user can delete this event
      if (
        req.user.role !== "admin" &&
        event.createdBy.toString() !== req.user._id.toString()
      ) {
        return res.status(403).json({
          success: false,
          message: "You can only delete your own events",
        });
      }

      // Delete associated files
      if (event.attachments && event.attachments.length > 0) {
        event.attachments.forEach((attachment) => {
          if (attachment.filename) {
            const filePath = path.join(
              uploadDir,
              "events",
              attachment.filename
            );
            deleteFile(filePath);
          }
        });
      }

      await Event.findByIdAndDelete(req.params.id);

      // Log activity after event is deleted
      try {
        await ActivityLog.logActivity({
          user: req.user._id,
          action: "delete_event",
          resource: "event",
          resourceId: event._id,
          details: {
            method: req.method,
            url: req.originalUrl,
            eventTitle: event.title,
          },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.get("User-Agent"),
          success: true,
        });
      } catch (logError) {
        console.error("Error logging delete event activity:", logError);
      }

      res.json({
        success: true,
        message: "Event deleted successfully",
      });
    } catch (error) {
      console.error("Delete event error:", error);
      res.status(500).json({
        success: false,
        message: "Server error while deleting event",
      });
    }
  }
);

// @route   GET /api/events/export
// @desc    Export events to CSV/JSON
// @access  Private (Admin/Editor)
router.get(
  "/export/data",
  authenticate,
  checkPermission("canExportData"),
  async (req, res) => {
    try {
      const format = req.query.format || "json";
      const startDate = req.query.startDate;
      const endDate = req.query.endDate;

      console.log("=== EXPORT REQUEST ===");
      console.log("Format:", format);
      console.log("Query params:", req.query);
      console.log("User role:", req.user?.role);

      let query = {};

      if (req.user.role !== "admin") {
        query.createdBy = req.user._id;
      }

      if (startDate && endDate) {
        query.startDate = {
          $gte: new Date(startDate),
          $lte: new Date(endDate),
        };
      }

      const events = await Event.find(query)
        .populate("organizer", "firstName lastName email")
        .populate("createdBy", "firstName lastName")
        .sort({ startDate: 1 });

      if (format === "csv") {
        // Convert to comprehensive CSV format for church use
        const csvData = events.map((event) => {
          // Format dates for readability
          const formatDate = (date) => {
            return new Date(date).toLocaleDateString("en-US", {
              weekday: "long",
              year: "numeric",
              month: "long",
              day: "numeric",
            });
          };

          // Format time for readability
          const formatTime = (time) => {
            if (!time) return "";
            const [hours, minutes] = time.split(":");
            const hour = parseInt(hours, 10);
            const ampm = hour >= 12 ? "PM" : "AM";
            const displayHour = hour % 12 || 12;
            return `${displayHour}:${minutes} ${ampm}`;
          };

          // Format recurrence info
          const getRecurrenceInfo = (event) => {
            if (!event.isRecurring || !event.recurrence)
              return "One-time event";

            let recurrenceText = `Every ${event.recurrence.interval} ${event.recurrence.pattern}`;
            if (event.recurrence.interval > 1) recurrenceText += "s";

            if (
              event.recurrence.pattern === "weekly" &&
              event.recurrence.daysOfWeek?.length > 0
            ) {
              const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
              const selectedDays = event.recurrence.daysOfWeek
                .map((day) => days[day])
                .join(", ");
              recurrenceText += ` on ${selectedDays}`;
            }

            if (event.recurrence.endDate) {
              recurrenceText += ` until ${formatDate(
                event.recurrence.endDate
              )}`;
            }

            return recurrenceText;
          };

          return {
            "Event Title": event.title || "",
            Description: event.description || "",
            "Start Date": formatDate(event.startDate),
            "End Date": formatDate(event.endDate),
            "Start Time": formatTime(event.startTime),
            "End Time": formatTime(event.endTime),
            "Location Name": event.location?.name || "",
            "Location Address": event.location?.address || "",
            Category: event.category || "",
            Priority: event.priority || "",
            Status: event.status || "",
            Recurrence: getRecurrenceInfo(event),
            "Expected Attendees": event.attendees?.expected || "",
            "Registration Required": event.attendees?.requiresRegistration
              ? "Yes"
              : "No",
            "Registration Deadline": event.attendees?.registrationDeadline
              ? formatDate(event.attendees.registrationDeadline)
              : "",
            "Contact Person": event.pointOfContact?.name || "",
            "Contact Email": event.pointOfContact?.email || "",
            "Contact Phone": event.pointOfContact?.phone || "",
            Organizer:
              event.organizer?.firstName && event.organizer?.lastName
                ? `${event.organizer.firstName} ${event.organizer.lastName}`
                : "",
            "Organizer Email": event.organizer?.email || "",
            "Public Event": event.isPublic ? "Yes" : "No",
            Tags: event.tags?.join(", ") || "",
            "Created By":
              event.createdBy?.firstName && event.createdBy?.lastName
                ? `${event.createdBy.firstName} ${event.createdBy.lastName}`
                : "",
            "Created Date": formatDate(event.createdAt),
            "Last Modified": event.updatedAt ? formatDate(event.updatedAt) : "",
            Notes: event.notes || "",
          };
        });

        // Generate filename with current date
        const currentDate = new Date().toISOString().split("T")[0];
        const filename = `church-events-${currentDate}.csv`;

        res.setHeader("Content-Type", "text/csv; charset=utf-8");
        res.setHeader(
          "Content-Disposition",
          `attachment; filename="${filename}"`
        );

        // Proper CSV conversion with escaping
        const escapeCsvValue = (value) => {
          if (value === null || value === undefined) return "";
          const stringValue = String(value);
          // Escape quotes and wrap in quotes if contains comma, quote, or newline
          if (
            stringValue.includes(",") ||
            stringValue.includes('"') ||
            stringValue.includes("\n")
          ) {
            return `"${stringValue.replace(/"/g, '""')}"`;
          }
          return stringValue;
        };

        if (csvData.length > 0) {
          const csvHeaders = Object.keys(csvData[0])
            .map(escapeCsvValue)
            .join(",");
          const csvRows = csvData.map((row) =>
            Object.values(row).map(escapeCsvValue).join(",")
          );
          const csvContent = [csvHeaders, ...csvRows].join("\n");

          // Add BOM for proper UTF-8 encoding in Excel
          res.send("\ufeff" + csvContent);
        } else {
          res.send("No events found for export");
        }
      } else if (format === "pdf") {
        // Generate PDF
        console.log("=== PDF GENERATION STARTED ===");
        console.log(`Events count: ${events.length}`);
        try {
          console.log(`Generating PDF for ${events.length} events`);
          const pdfBuffer = await generatePDF(events);
          const currentDate = new Date().toISOString().split("T")[0];
          const filename = `grace-bible-fellowship-events-${currentDate}.pdf`;

          console.log(
            `PDF generated successfully. Buffer size: ${pdfBuffer.length} bytes`
          );
          console.log(`Buffer type: ${typeof pdfBuffer}`);
          console.log(`Is Buffer: ${Buffer.isBuffer(pdfBuffer)}`);
          console.log(
            `First 10 bytes: ${pdfBuffer.slice(0, 10).toString("hex")}`
          );

          res.setHeader("Content-Type", "application/pdf");
          res.setHeader(
            "Content-Disposition",
            `attachment; filename="${filename}"`
          );
          res.setHeader("Content-Length", pdfBuffer.length);

          res.end(pdfBuffer, "binary");
        } catch (pdfError) {
          console.error("PDF generation error:", pdfError);
          res.status(500).json({
            success: false,
            message: "Error generating PDF export",
          });
        }
      } else if (format === "word" || format === "docx") {
        // Generate Word document
        try {
          const wordBuffer = await generateWord(events);
          const currentDate = new Date().toISOString().split("T")[0];
          const filename = `grace-bible-fellowship-events-${currentDate}.docx`;

          console.log(
            `Word generated successfully. Buffer size: ${wordBuffer.length} bytes`
          );
          console.log(`Buffer type: ${typeof wordBuffer}`);
          console.log(`Is Buffer: ${Buffer.isBuffer(wordBuffer)}`);

          res.setHeader(
            "Content-Type",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
          );
          res.setHeader(
            "Content-Disposition",
            `attachment; filename="${filename}"`
          );
          res.setHeader("Content-Length", wordBuffer.length);

          res.end(wordBuffer, "binary");
        } catch (wordError) {
          console.error("Word generation error:", wordError);
          res.status(500).json({
            success: false,
            message: "Error generating Word export",
          });
        }
      } else {
        // Default JSON format
        console.log("=== FALLING BACK TO JSON ===");
        console.log("Format was:", format);
        console.log("Format type:", typeof format);
        console.log("Format === 'pdf':", format === "pdf");
        res.json({
          success: true,
          data: {
            events,
            exportedAt: new Date().toISOString(),
            totalEvents: events.length,
          },
        });
      }
    } catch (error) {
      console.error("Export events error:", error);
      res.status(500).json({
        success: false,
        message: "Server error while exporting events",
      });
    }
  }
);

// @route   GET /api/events/calendar/:year/:month
// @desc    Get events for calendar view
// @access  Public
router.get("/calendar/:year/:month", optionalAuth, async (req, res) => {
  try {
    const year = parseInt(req.params.year);
    const month = parseInt(req.params.month);

    // Validate year and month parameters
    if (isNaN(year) || isNaN(month)) {
      return res.status(400).json({
        success: false,
        message: "Invalid year or month parameter",
      });
    }

    if (year < 1900 || year > 2100) {
      return res.status(400).json({
        success: false,
        message: "Year must be between 1900 and 2100",
      });
    }

    if (month < 1 || month > 12) {
      return res.status(400).json({
        success: false,
        message: "Month must be between 1 and 12",
      });
    }

    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);

    let query = {
      $or: [
        {
          startDate: {
            $gte: startDate,
            $lte: endDate,
          },
        },
        {
          endDate: {
            $gte: startDate,
            $lte: endDate,
          },
        },
      ],
    };

    if (!req.user) {
      query.isPublic = true;
      query.status = "published";
    }

    const events = await Event.find(query)
      .populate("organizer", "firstName lastName")
      .sort({ startDate: 1 });

    res.json({
      success: true,
      data: {
        events,
        month: month,
        year: year,
      },
    });
  } catch (error) {
    console.error("Get calendar events error:", error);
    res.status(500).json({
      success: false,
      message: "Server error while fetching calendar events",
    });
  }
});

// @route   POST /api/events/upload
// @desc    Upload files for events
// @access  Private (Admin/Editor)
router.post(
  "/upload",
  authenticate,
  checkPermission("canCreateEvents"),
  upload.array("files", 10),
  handleUploadError,
  async (req, res) => {
    try {
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({
          success: false,
          message: "No files uploaded",
        });
      }

      // Additional server-side validation
      const maxFileSize =
        parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024; // 5MB
      const maxFiles = 10;

      if (req.files.length > maxFiles) {
        return res.status(400).json({
          success: false,
          message: `Too many files. Maximum ${maxFiles} files allowed.`,
        });
      }

      // Validate each file
      for (const file of req.files) {
        if (file.size > maxFileSize) {
          return res.status(400).json({
            success: false,
            message: `File "${
              file.originalname
            }" is too large. Maximum size is ${Math.round(
              maxFileSize / (1024 * 1024)
            )}MB.`,
          });
        }

        // Additional filename validation
        if (!file.originalname || file.originalname.trim() === "") {
          return res.status(400).json({
            success: false,
            message: "Invalid filename",
          });
        }

        // Check for null bytes in filename (security)
        if (file.originalname.includes("\0")) {
          return res.status(400).json({
            success: false,
            message: "Invalid filename contains null bytes",
          });
        }
      }

      const uploadedFiles = req.files.map((file) => {
        const fileInfo = getFileInfo(file);
        // Add metadata for cleanup and tracking
        fileInfo.uploadedAt = new Date();
        fileInfo.uploadedBy = req.user._id;
        return fileInfo;
      });

      // Log activity after files are uploaded
      try {
        await ActivityLog.logActivity({
          user: req.user._id,
          action: "upload_files",
          resource: "system",
          details: {
            method: req.method,
            url: req.originalUrl,
            fileCount: uploadedFiles.length,
            fileNames: uploadedFiles.map((f) => f.name),
          },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.get("User-Agent"),
          success: true,
        });
      } catch (logError) {
        console.error("Error logging upload files activity:", logError);
      }

      res.status(200).json({
        success: true,
        message: `${uploadedFiles.length} file(s) uploaded successfully`,
        data: {
          files: uploadedFiles,
        },
      });
    } catch (error) {
      console.error("File upload error:", error);
      res.status(500).json({
        success: false,
        message: "Server error while uploading files",
      });
    }
  }
);

// @route   DELETE /api/events/upload/:filename
// @desc    Delete uploaded file
// @access  Private (Admin/Editor)
router.delete(
  "/upload/:filename",
  authenticate,
  checkPermission("canEditEvents"),
  async (req, res) => {
    try {
      const filename = req.params.filename;
      const filePath = path.join(uploadDir, "events", filename);

      const deleted = deleteFile(filePath);

      if (deleted) {
        // Log activity after file is deleted
        try {
          await ActivityLog.logActivity({
            user: req.user._id,
            action: "delete_file",
            resource: "system",
            details: {
              method: req.method,
              url: req.originalUrl,
              filename: filename,
            },
            ipAddress: req.ip || req.connection.remoteAddress,
            userAgent: req.get("User-Agent"),
            success: true,
          });
        } catch (logError) {
          console.error("Error logging delete file activity:", logError);
        }

        res.status(200).json({
          success: true,
          message: "File deleted successfully",
        });
      } else {
        res.status(404).json({
          success: false,
          message: "File not found",
        });
      }
    } catch (error) {
      console.error("File deletion error:", error);
      res.status(500).json({
        success: false,
        message: "Server error while deleting file",
      });
    }
  }
);

module.exports = router;
