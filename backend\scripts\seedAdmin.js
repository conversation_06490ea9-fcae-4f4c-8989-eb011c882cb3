const mongoose = require("mongoose");
const { User } = require("../models");
require("dotenv").config();

const seedUsers = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(
      process.env.MONGODB_URI || "mongodb://localhost:27017/church-events"
    );
    console.log("Connected to MongoDB");

    // Check if users already exist
    const existingAdmin = await User.findOne({ role: "admin" });
    const existingEditor = await User.findOne({ role: "editor" });

    if (existingAdmin && existingEditor) {
      console.log("Users already exist:");
      console.log("Admin:", existingAdmin.email);
      console.log("Editor:", existingEditor.email);
      process.exit(0);
    }

    // Create admin user if doesn't exist
    if (!existingAdmin) {
      const adminData = {
        username: "admin",
        email: "<EMAIL>",
        password: "admin123",
        firstName: "Church",
        lastName: "Administrator",
        role: "admin",
        phone: "+1234567890",
        isActive: true,
        permissions: {
          canCreateEvents: true,
          canEditEvents: true,
          canDeleteEvents: true,
          canManageUsers: true,
          canExportData: true,
        },
      };

      const admin = new User(adminData);
      await admin.save();
      console.log("✅ Admin user created successfully!");
      console.log("   Email: <EMAIL>");
      console.log("   Password: admin123");
    } else {
      console.log("✅ Admin user already exists:", existingAdmin.email);
    }

    // Create editor user if doesn't exist
    if (!existingEditor) {
      const editorData = {
        username: "editor",
        email: "<EMAIL>",
        password: "editor123",
        firstName: "John",
        lastName: "Editor",
        role: "editor",
        phone: "+1987654321",
        isActive: true,
        permissions: {
          canCreateEvents: true,
          canEditEvents: true,
          canDeleteEvents: false, // Editors can't delete events
          canManageUsers: false, // Editors can't manage users
          canExportData: true,
        },
      };

      const editor = new User(editorData);
      await editor.save();
      console.log("✅ Editor user created successfully!");
      console.log("   Email: <EMAIL>");
      console.log("   Password: editor123");
    } else {
      console.log("✅ Editor user already exists:", existingEditor.email);
    }

    console.log("\n🎉 User seeding completed!");
    console.log("\n📋 Test Accounts:");
    console.log("┌─────────────────────────────────────────────────┐");
    console.log("│ ADMIN ACCOUNT                                   │");
    console.log("│ Email:    <EMAIL>                      │");
    console.log("│ Password: admin123                              │");
    console.log("│ Role:     Administrator                         │");
    console.log("│ Can:      Create/Edit/Delete Events & Users     │");
    console.log("├─────────────────────────────────────────────────┤");
    console.log("│ EDITOR ACCOUNT                                  │");
    console.log("│ Email:    <EMAIL>                     │");
    console.log("│ Password: editor123                             │");
    console.log("│ Role:     Editor                                │");
    console.log("│ Can:      Create/Edit Events (No Delete/Users)  │");
    console.log("└─────────────────────────────────────────────────┘");
    console.log("\n⚠️  IMPORTANT: Change passwords after first login!");

    process.exit(0);
  } catch (error) {
    console.error("Error seeding users:", error);
    process.exit(1);
  }
};

// Run the seed function
seedUsers();
