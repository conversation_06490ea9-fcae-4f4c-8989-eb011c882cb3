import React from "react";
import styled from "styled-components";
import { motion } from "framer-motion";
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Edit,
  Trash2,
  Eye,
  Paperclip,
} from "lucide-react";
import {
  formatDate,
  formatTime,
  getRelativeDate,
  isToday,
  isPast,
} from "../../utils/dateUtils";
import { EVENT_CATEGORIES } from "../../utils/constants";
import { useAuth } from "../../hooks/useAuth";

const Card = styled(motion.div)`
  background: ${({ theme }) => theme.colors.background.primary};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  border-left: 4px solid
    ${({ category }) =>
      category === "worship"
        ? "#8b5cf6"
        : category === "fellowship"
        ? "#06b6d4"
        : category === "study"
        ? "#10b981"
        : category === "service"
        ? "#f59e0b"
        : category === "prayer"
        ? "#ec4899"
        : "#3b82f6"};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[6]};
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: ${({ isPast }) => (isPast ? 0.7 : 1)};
  position: relative;
  box-shadow: ${({ theme }) => theme.shadows.sm};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${({ theme }) => theme.shadows.md};
  }

  /* Mobile-specific improvements */
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing[4]};

    /* Better touch feedback */
    &:active {
      transform: scale(0.98);
      transition: transform 0.1s ease;
    }

    /* Remove hover effects on touch devices */
    @media (hover: none) {
      &:hover {
        transform: none;
        box-shadow: ${({ theme }) => theme.shadows.md};
      }
    }
  }
`;

const CardHeader = styled.div`
  position: relative;
  z-index: 2;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const EventDateBadge = styled.div`
  display: inline-flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  background: ${({ isToday, isPast }) => {
    if (isToday) return "#10b981";
    if (isPast) return "#6b7280";
    return "#3b82f6";
  }};
  color: white;
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  margin-bottom: ${({ theme }) => theme.spacing[3]};
`;

const EventTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes["2xl"]};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0 0 ${({ theme }) => theme.spacing[3]} 0;
  line-height: ${({ theme }) => theme.lineHeights.tight};
  position: relative;
  z-index: 2;
  letter-spacing: -0.025em;

  /* Add subtle text shadow for depth */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  /* Spiritual emphasis for certain words */
  &::first-letter {
    font-size: 1.2em;
    font-weight: 800;
    color: ${({ theme }) => theme.colors.primary[600]};
  }

  @media (max-width: 768px) {
    font-size: ${({ theme }) => theme.fontSizes.xl};
  }
`;

const EventActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[1]};
  opacity: 0;
  transition: opacity ${({ theme }) => theme.transitions.fast};

  ${Card}:hover & {
    opacity: 1;
  }
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background: ${({ theme, variant }) => {
    switch (variant) {
      case "edit":
        return theme.colors.info[100];
      case "delete":
        return theme.colors.error[100];
      default:
        return theme.colors.gray[100];
    }
  }};
  color: ${({ theme, variant }) => {
    switch (variant) {
      case "edit":
        return theme.colors.info[600];
      case "delete":
        return theme.colors.error[600];
      default:
        return theme.colors.gray[600];
    }
  }};
  transition: all ${({ theme }) => theme.transitions.fast};

  &:hover {
    transform: scale(1.1);
  }

  /* Larger touch targets on mobile */
  @media (max-width: 768px) {
    width: 40px;
    height: 40px;

    /* Add touch-friendly spacing */
    &:not(:last-child) {
      margin-right: ${({ theme }) => theme.spacing[2]};
    }
  }
`;

const EventMeta = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${({ theme }) => theme.spacing[3]};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  position: relative;
  z-index: 2;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: ${({ theme }) => theme.spacing[2]};
  }
`;

const MetaItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  background: ${({ theme }) => theme.colors.background.secondary};
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme }) => theme.colors.primary[50]};
    border-color: ${({ theme }) => theme.colors.primary[200]};
  }
`;

const MetaIcon = styled.div`
  color: ${({ theme }) => theme.colors.primary[600]};
  display: flex;
  align-items: center;
  background: ${({ theme }) => theme.colors.primary[100]};
  padding: ${({ theme }) => theme.spacing[1]};
  border-radius: ${({ theme }) => theme.borderRadius.md};

  svg {
    width: 14px;
    height: 14px;
  }
`;

const EventDescription = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  line-height: ${({ theme }) => theme.lineHeights.relaxed};
  margin: 0 0 ${({ theme }) => theme.spacing[4]} 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  position: relative;
  z-index: 2;
`;

const AttendanceInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors.primary[50]},
    ${({ theme }) => theme.colors.secondary[50]}
  );
  padding: ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  border: 1px solid ${({ theme }) => theme.colors.primary[100]};
  position: relative;
  z-index: 2;
`;

const AttendanceIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: ${({ theme }) => theme.colors.primary[100]};
  color: ${({ theme }) => theme.colors.primary[600]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};

  svg {
    width: 16px;
    height: 16px;
  }
`;

const AttendanceText = styled.div`
  flex: 1;

  .count {
    font-weight: ${({ theme }) => theme.fontWeights.bold};
    color: ${({ theme }) => theme.colors.primary[700]};
    font-size: ${({ theme }) => theme.fontSizes.sm};
  }

  .label {
    font-size: ${({ theme }) => theme.fontSizes.xs};
    color: ${({ theme }) => theme.colors.text.secondary};
    margin-top: 2px;
  }
`;

const EventFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing[3]};
  position: relative;
  z-index: 2;
  padding-top: ${({ theme }) => theme.spacing[3]};
  border-top: 1px solid ${({ theme }) => theme.colors.border.light};
`;

const BadgeGroup = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  flex-wrap: wrap;
`;

const CategoryBadge = styled.span`
  display: inline-flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[3]};
  background: ${({ theme, category }) => {
    const categoryData = EVENT_CATEGORIES.find((cat) => cat.value === category);
    return categoryData ? theme.colors.primary[100] : theme.colors.gray[100];
  }};
  color: ${({ theme, category }) => {
    const categoryData = EVENT_CATEGORIES.find((cat) => cat.value === category);
    return categoryData ? theme.colors.primary[700] : theme.colors.gray[700];
  }};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid
    ${({ theme, category }) => {
      const categoryData = EVENT_CATEGORIES.find(
        (cat) => cat.value === category
      );
      return categoryData ? theme.colors.primary[200] : theme.colors.gray[200];
    }};

  svg {
    width: 12px;
    height: 12px;
  }
`;

const StatusBadge = styled.span`
  display: inline-flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  background: ${({ theme, status }) => {
    switch (status) {
      case "published":
        return theme.colors.success[100];
      case "draft":
        return theme.colors.gray[100];
      case "cancelled":
        return theme.colors.error[100];
      case "completed":
        return theme.colors.info[100];
      default:
        return theme.colors.gray[100];
    }
  }};
  color: ${({ theme, status }) => {
    switch (status) {
      case "published":
        return theme.colors.success[800];
      case "draft":
        return theme.colors.gray[800];
      case "cancelled":
        return theme.colors.error[800];
      case "completed":
        return theme.colors.info[800];
      default:
        return theme.colors.gray[800];
    }
  }};
`;

const DateBadge = styled.div`
  background: ${({ theme, isToday }) =>
    isToday ? theme.colors.primary[500] : theme.colors.background.secondary};
  color: ${({ theme, isToday }) =>
    isToday ? "white" : theme.colors.text.secondary};
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  text-align: center;
  min-width: 80px;
`;

const EventCard = ({
  event,
  onClick,
  onEdit,
  onDelete,
  showActions = true,
}) => {
  const { isAuthenticated, hasPermission } = useAuth();

  const eventDate = new Date(event.startDate);
  const eventIsPast = isPast(eventDate);
  const eventIsToday = isToday(eventDate);

  const getCategoryLabel = (categoryValue) => {
    const category = EVENT_CATEGORIES.find(
      (cat) => cat.value === categoryValue
    );
    return category ? category.label : categoryValue;
  };

  const handleCardClick = () => {
    if (onClick) {
      onClick(event);
    }
  };

  const handleEdit = (e) => {
    e.stopPropagation();
    if (onEdit) {
      onEdit(event);
    }
  };

  const handleDelete = (e) => {
    e.stopPropagation();
    if (onDelete) {
      onDelete(event);
    }
  };

  const canEdit = isAuthenticated && hasPermission("canEditEvents");
  const canDelete = isAuthenticated && hasPermission("canDeleteEvents");

  return (
    <Card
      category={event.category}
      isPast={eventIsPast}
      onClick={handleCardClick}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <CardHeader>
        <EventDateBadge isToday={eventIsToday} isPast={eventIsPast}>
          <Calendar size={12} />
          {eventIsToday
            ? "Today"
            : eventIsPast
            ? "Past Event"
            : getRelativeDate(eventDate)}
        </EventDateBadge>

        <EventTitle>{event.title}</EventTitle>

        {showActions && isAuthenticated && (
          <EventActions>
            <ActionButton onClick={handleCardClick} title="View Details">
              <Eye size={14} />
            </ActionButton>
            {canEdit && (
              <ActionButton
                variant="edit"
                onClick={handleEdit}
                title="Edit Event"
              >
                <Edit size={14} />
              </ActionButton>
            )}
            {canDelete && (
              <ActionButton
                variant="delete"
                onClick={handleDelete}
                title="Delete Event"
              >
                <Trash2 size={14} />
              </ActionButton>
            )}
          </EventActions>
        )}
      </CardHeader>

      <EventMeta>
        <MetaItem>
          <MetaIcon>
            <Clock size={16} />
          </MetaIcon>
          {formatTime(event.startTime)} - {formatTime(event.endTime)}
        </MetaItem>

        {event.location?.name && (
          <MetaItem>
            <MetaIcon>
              <MapPin size={16} />
            </MetaIcon>
            {event.location.name}
          </MetaItem>
        )}
      </EventMeta>

      {event.description && (
        <EventDescription>{event.description}</EventDescription>
      )}

      {/* Attendance/Engagement Info */}
      {(event.attendees?.expected > 0 || event.attachments?.length > 0) && (
        <AttendanceInfo>
          {event.attendees?.expected > 0 && (
            <>
              <AttendanceIcon>
                <Users size={16} />
              </AttendanceIcon>
              <AttendanceText>
                <div className="count">{event.attendees.expected} people</div>
                <div className="label">Expected to attend</div>
              </AttendanceText>
            </>
          )}

          {event.attachments?.length > 0 && (
            <>
              <AttendanceIcon>
                <Paperclip size={16} />
              </AttendanceIcon>
              <AttendanceText>
                <div className="count">
                  {event.attachments.length} resources
                </div>
                <div className="label">Available for download</div>
              </AttendanceText>
            </>
          )}
        </AttendanceInfo>
      )}

      <EventFooter>
        <BadgeGroup>
          <CategoryBadge category={event.category}>
            {getCategoryLabel(event.category)}
          </CategoryBadge>
          <StatusBadge status={event.status}>{event.status}</StatusBadge>
        </BadgeGroup>

        <DateBadge isToday={eventIsToday}>
          {formatDate(eventDate, "MMM dd")}
        </DateBadge>
      </EventFooter>
    </Card>
  );
};

export default EventCard;
