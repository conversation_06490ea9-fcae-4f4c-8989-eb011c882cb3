import styled, { createGlobalStyle } from "styled-components";
import { theme } from "./theme";

export const GlobalStyles = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    font-size: 16px;
    scroll-behavior: smooth;
  }

  body {
    font-family: ${theme.fonts.primary};
    font-size: ${theme.fontSizes.base};
    font-weight: ${theme.fontWeights.normal};
    line-height: ${theme.lineHeights.normal};
    color: ${theme.colors.text.primary};
    background-color: ${theme.colors.background.primary};
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
  }

  #root {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  /* Headings */
  h1, h2, h3, h4, h5, h6 {
    font-family: ${theme.fonts.heading};
    font-weight: ${theme.fontWeights.semibold};
    line-height: ${theme.lineHeights.tight};
    color: ${theme.colors.text.primary};
    margin-bottom: ${theme.spacing[4]};
  }

  h1 {
    font-size: ${theme.fontSizes["4xl"]};
    font-weight: ${theme.fontWeights.bold};
  }

  h2 {
    font-size: ${theme.fontSizes["3xl"]};
  }

  h3 {
    font-size: ${theme.fontSizes["2xl"]};
  }

  h4 {
    font-size: ${theme.fontSizes.xl};
  }

  h5 {
    font-size: ${theme.fontSizes.lg};
  }

  h6 {
    font-size: ${theme.fontSizes.base};
  }

  /* Links */
  a {
    color: ${theme.colors.primary[600]};
    text-decoration: none;
    transition: color ${theme.transitions.fast};

    &:hover {
      color: ${theme.colors.primary[700]};
      text-decoration: underline;
    }

    &:focus {
      outline: 2px solid ${theme.colors.primary[500]};
      outline-offset: 2px;
    }
  }

  /* Buttons */
  button {
    font-family: inherit;
    cursor: pointer;
    border: none;
    background: none;
    transition: all ${theme.transitions.fast};

    &:focus {
      outline: 2px solid ${theme.colors.primary[500]};
      outline-offset: 2px;
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }

  /* Form elements */
  input, textarea, select {
    font-family: inherit;
    font-size: inherit;
    border: 1px solid ${theme.colors.border.medium};
    border-radius: ${theme.borderRadius.md};
    padding: ${theme.spacing[3]} ${theme.spacing[4]};
    transition: border-color ${theme.transitions.fast}, box-shadow ${theme.transitions.fast};

    &:focus {
      outline: none;
      border-color: ${theme.colors.primary[500]};
      box-shadow: 0 0 0 3px ${theme.colors.primary[100]};
    }

    &:disabled {
      background-color: ${theme.colors.background.tertiary};
      cursor: not-allowed;
      opacity: 0.6;
    }

    &::placeholder {
      color: ${theme.colors.text.tertiary};
    }
  }

  /* Lists */
  ul, ol {
    margin-bottom: ${theme.spacing[4]};
    padding-left: ${theme.spacing[6]};
  }

  li {
    margin-bottom: ${theme.spacing[2]};
  }

  /* Paragraphs */
  p {
    margin-bottom: ${theme.spacing[4]};
    line-height: ${theme.lineHeights.relaxed};
  }

  /* Images */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Tables */
  table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: ${theme.spacing[6]};
  }

  th, td {
    padding: ${theme.spacing[3]} ${theme.spacing[4]};
    text-align: left;
    border-bottom: 1px solid ${theme.colors.border.light};
  }

  th {
    font-weight: ${theme.fontWeights.semibold};
    background-color: ${theme.colors.background.secondary};
  }

  /* Scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: ${theme.colors.background.secondary};
  }

  ::-webkit-scrollbar-thumb {
    background: ${theme.colors.gray[400]};
    border-radius: ${theme.borderRadius.full};

    &:hover {
      background: ${theme.colors.gray[500]};
    }
  }

  /* Selection */
  ::selection {
    background-color: ${theme.colors.primary[200]};
    color: ${theme.colors.primary[900]};
  }

  /* Focus visible for better accessibility */
  :focus-visible {
    outline: 2px solid ${theme.colors.primary[500]};
    outline-offset: 2px;
  }

  /* Print styles */
  @media print {
    * {
      background: transparent !important;
      color: black !important;
      box-shadow: none !important;
      text-shadow: none !important;
    }

    a, a:visited {
      text-decoration: underline;
    }

    a[href]:after {
      content: " (" attr(href) ")";
    }

    abbr[title]:after {
      content: " (" attr(title) ")";
    }

    .no-print {
      display: none !important;
    }
  }

  /* Mobile-first responsive typography and interactions */
  @media (max-width: ${theme.breakpoints.sm}) {
    html {
      font-size: 16px; /* Keep 16px for better mobile readability */
    }

    h1 {
      font-size: ${theme.fontSizes["3xl"]};
      line-height: 1.2;
    }

    h2 {
      font-size: ${theme.fontSizes["2xl"]};
      line-height: 1.3;
    }

    h3 {
      font-size: ${theme.fontSizes.xl};
      line-height: 1.4;
    }

    /* Better touch targets */
    button,
    [role="button"],
    input[type="submit"],
    input[type="button"] {
      min-height: 44px;
      min-width: 44px;
      touch-action: manipulation;
      -webkit-tap-highlight-color: transparent;
    }

    /* Improved form elements for mobile */
    input,
    textarea,
    select {
      font-size: 16px; /* Prevent zoom on iOS */
      padding: ${theme.spacing[3]} ${theme.spacing[4]};
      border-radius: ${theme.borderRadius.lg};
    }

    /* Better spacing for mobile */
    .container {
      padding-left: ${theme.spacing[4]};
      padding-right: ${theme.spacing[4]};
    }

    /* HIDE SCROLLBARS ON MOBILE - Native app feel */
    ::-webkit-scrollbar {
      display: none;
      width: 0;
      height: 0;
    }

    /* For Firefox */
    * {
      scrollbar-width: none;
    }

    /* For IE and Edge */
    * {
      -ms-overflow-style: none;
    }

    /* Ensure scrolling still works */
    body, html {
      overflow-x: hidden;
      -webkit-overflow-scrolling: touch;
    }
  }

  /* Mobile and tablet devices - hide scrollbars for native feel */
  @media (max-width: 768px) {
    /* Hide scrollbars completely on mobile/tablet */
    ::-webkit-scrollbar {
      display: none !important;
      width: 0 !important;
      height: 0 !important;
    }

    /* For Firefox */
    * {
      scrollbar-width: none !important;
    }

    /* For IE and Edge */
    * {
      -ms-overflow-style: none !important;
    }

    /* Ensure smooth scrolling on mobile */
    body, html, #root {
      -webkit-overflow-scrolling: touch;
      overflow-x: hidden;
    }
  }

  /* PWA and standalone mode optimizations */
  @media (display-mode: standalone) {
    body {
      -webkit-user-select: none;
      -webkit-touch-callout: none;
      -webkit-tap-highlight-color: transparent;
    }

    /* Hide scrollbars in standalone mode for cleaner look */
    ::-webkit-scrollbar {
      display: none;
    }

    /* Support for safe areas */
    .safe-area-top {
      padding-top: env(safe-area-inset-top);
    }

    .safe-area-bottom {
      padding-bottom: env(safe-area-inset-bottom);
    }

    .safe-area-left {
      padding-left: env(safe-area-inset-left);
    }

    .safe-area-right {
      padding-right: env(safe-area-inset-right);
    }
  }

  /* High DPI displays */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Sharper borders and shadows for retina displays */
    * {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
  }

  /* Reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  /* Dark mode support (future enhancement) */
  @media (prefers-color-scheme: dark) {
    /* Dark mode styles would go here */
  }

  /* Utility classes */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .hidden {
    display: none;
  }

  .block {
    display: block;
  }

  .inline-block {
    display: inline-block;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }
`;
