import { create } from "zustand";
import { eventsAPI } from "../api";

const useEventsStore = create((set, get) => ({
  // State
  events: [],
  currentEvent: null,
  calendarEvents: [],
  isLoading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  },
  filters: {
    category: "",
    startDate: "",
    endDate: "",
    search: "",
    status: "published",
  },
  // Rate limiting state
  lastFetchTime: 0,
  pendingFetch: null,
  lastFetchParams: null,

  // Actions
  fetchEvents: async (params = {}) => {
    const state = get();
    const now = Date.now();
    const minInterval = 500; // Minimum 500ms between requests

    const requestParams = {
      ...state.filters,
      ...params,
    };

    // Check if this is the same request as the last one
    const paramsString = JSON.stringify(requestParams);
    if (
      state.lastFetchParams === paramsString &&
      now - state.lastFetchTime < 2000
    ) {
      console.log("Skipping duplicate request:", paramsString);
      return Promise.resolve({
        data: { events: state.events, pagination: state.pagination },
      });
    }

    // Cancel any pending fetch
    if (state.pendingFetch) {
      clearTimeout(state.pendingFetch);
    }

    // If we just made a request recently, debounce it
    if (now - state.lastFetchTime < minInterval) {
      return new Promise((resolve, reject) => {
        const timeoutId = setTimeout(async () => {
          try {
            const result = await get().fetchEvents(params);
            resolve(result);
          } catch (error) {
            reject(error);
          }
        }, minInterval - (now - state.lastFetchTime));

        set({ pendingFetch: timeoutId });
      });
    }

    set({
      isLoading: true,
      error: null,
      lastFetchTime: now,
      pendingFetch: null,
      lastFetchParams: paramsString,
    });

    try {
      const response = await eventsAPI.getEvents(requestParams);

      set({
        events: response.data.events,
        pagination: response.data.pagination,
        isLoading: false,
        error: null,
      });

      return response;
    } catch (error) {
      let errorMessage = "Failed to fetch events";

      if (error.response?.status === 429) {
        errorMessage = "Too many requests. Please wait a moment and try again.";
        console.log("Rate limit hit, will retry automatically");

        // Auto-retry after a delay for rate limit errors
        setTimeout(() => {
          get().fetchEvents(params);
        }, 2000);
      } else {
        errorMessage = error.response?.data?.message || errorMessage;
      }

      set({
        events: [],
        isLoading: false,
        error: errorMessage,
      });
      throw error;
    }
  },

  fetchEvent: async (id) => {
    set({ isLoading: true, error: null });
    try {
      const response = await eventsAPI.getEvent(id);
      set({
        currentEvent: response.data.event,
        isLoading: false,
        error: null,
      });
      return response;
    } catch (error) {
      const errorMessage =
        error.response?.data?.message || "Failed to fetch event";
      set({
        currentEvent: null,
        isLoading: false,
        error: errorMessage,
      });
      throw error;
    }
  },

  createEvent: async (eventData) => {
    set({ isLoading: true, error: null });
    try {
      const response = await eventsAPI.createEvent(eventData);

      // Add new event to both lists
      set((state) => ({
        events: [response.data.event, ...state.events],
        calendarEvents: [response.data.event, ...state.calendarEvents],
        isLoading: false,
        error: null,
      }));

      return response;
    } catch (error) {
      const errorMessage =
        error.response?.data?.message || "Failed to create event";
      set({
        isLoading: false,
        error: errorMessage,
      });
      throw error;
    }
  },

  updateEvent: async (id, eventData) => {
    set({ isLoading: true, error: null });
    try {
      const response = await eventsAPI.updateEvent(id, eventData);

      // Update event in both lists
      set((state) => ({
        events: state.events.map((event) =>
          event._id === id ? response.data.event : event
        ),
        calendarEvents: state.calendarEvents.map((event) =>
          event._id === id ? response.data.event : event
        ),
        currentEvent: response.data.event,
        isLoading: false,
        error: null,
      }));

      return response;
    } catch (error) {
      const errorMessage =
        error.response?.data?.message || "Failed to update event";
      set({
        isLoading: false,
        error: errorMessage,
      });
      throw error;
    }
  },

  deleteEvent: async (id) => {
    set({ isLoading: true, error: null });
    try {
      await eventsAPI.deleteEvent(id);

      // Remove event from both lists
      set((state) => ({
        events: state.events.filter((event) => event._id !== id),
        calendarEvents: state.calendarEvents.filter(
          (event) => event._id !== id
        ),
        currentEvent:
          state.currentEvent?._id === id ? null : state.currentEvent,
        isLoading: false,
        error: null,
      }));
    } catch (error) {
      const errorMessage =
        error.response?.data?.message || "Failed to delete event";
      set({
        isLoading: false,
        error: errorMessage,
      });
      throw error;
    }
  },

  fetchCalendarEvents: async (year, month) => {
    set({ isLoading: true, error: null });
    try {
      const response = await eventsAPI.getCalendarEvents(year, month);
      set({
        calendarEvents: response.data.events,
        isLoading: false,
        error: null,
      });
      return response;
    } catch (error) {
      const errorMessage =
        error.response?.data?.message || "Failed to fetch calendar events";
      set({
        calendarEvents: [],
        isLoading: false,
        error: errorMessage,
      });
      throw error;
    }
  },

  searchEvents: async (query) => {
    set({ isLoading: true, error: null });
    try {
      const response = await eventsAPI.searchEvents(query, get().filters);
      set({
        events: response.data.events,
        pagination: response.data.pagination,
        isLoading: false,
        error: null,
      });
      return response;
    } catch (error) {
      const errorMessage = error.response?.data?.message || "Search failed";
      set({
        isLoading: false,
        error: errorMessage,
      });
      throw error;
    }
  },

  exportEvents: async (format = "json", dateRange = {}) => {
    set({ isLoading: true, error: null });
    try {
      const response = await eventsAPI.exportEvents({
        format,
        ...dateRange,
      });
      set({
        isLoading: false,
        error: null,
      });
      return response;
    } catch (error) {
      const errorMessage = error.response?.data?.message || "Export failed";
      set({
        isLoading: false,
        error: errorMessage,
      });
      throw error;
    }
  },

  // Filter actions
  setFilters: (newFilters) => {
    set((state) => ({
      filters: { ...state.filters, ...newFilters },
    }));
  },

  clearFilters: () => {
    set({
      filters: {
        category: "",
        startDate: "",
        endDate: "",
        search: "",
        status: "published",
      },
    });
  },

  // Utility actions
  clearError: () => {
    set({ error: null });
  },

  clearCurrentEvent: () => {
    set({ currentEvent: null });
  },
}));

export default useEventsStore;
