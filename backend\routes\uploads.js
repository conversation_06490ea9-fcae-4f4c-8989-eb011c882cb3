const express = require('express');
const path = require('path');
const fs = require('fs');
const { authenticate, optionalAuth } = require('../middleware/auth');
const { Event } = require('../models');

const router = express.Router();

// Get upload directory
const uploadDir = process.env.UPLOAD_PATH || './uploads';

// @route   GET /api/uploads/events/:filename
// @desc    Serve event files with access control
// @access  Public for public events, Private for private events
router.get('/events/:filename', optionalAuth, async (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(uploadDir, 'events', filename);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'File not found'
      });
    }

    // Find the event that contains this file
    const event = await Event.findOne({
      'attachments.filename': filename
    });

    if (!event) {
      return res.status(404).json({
        success: false,
        message: 'File not associated with any event'
      });
    }

    // Check access permissions
    if (!event.isPublic || event.status !== 'published') {
      // Private event or unpublished - require authentication
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required to access this file'
        });
      }

      // Check if user has permission to view this event
      const canView = req.user.role === 'admin' || 
                     event.createdBy.toString() === req.user._id.toString() ||
                     event.organizer.toString() === req.user._id.toString();

      if (!canView) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to access this file'
        });
      }
    }

    // Get file stats for headers
    const stats = fs.statSync(filePath);
    const fileExtension = path.extname(filename).toLowerCase();

    // Set appropriate content type
    const contentTypes = {
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      '.ppt': 'application/vnd.ms-powerpoint',
      '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      '.txt': 'text/plain',
      '.csv': 'text/csv',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp'
    };

    const contentType = contentTypes[fileExtension] || 'application/octet-stream';

    // Set headers
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Length', stats.size);
    res.setHeader('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year
    res.setHeader('ETag', `"${stats.mtime.getTime()}-${stats.size}"`);

    // Handle conditional requests
    const ifNoneMatch = req.headers['if-none-match'];
    const etag = `"${stats.mtime.getTime()}-${stats.size}"`;
    
    if (ifNoneMatch === etag) {
      return res.status(304).end();
    }

    // For images and PDFs, display inline; for others, suggest download
    if (contentType.startsWith('image/') || contentType === 'application/pdf') {
      res.setHeader('Content-Disposition', `inline; filename="${path.basename(filename)}"`);
    } else {
      res.setHeader('Content-Disposition', `attachment; filename="${path.basename(filename)}"`);
    }

    // Stream the file
    const fileStream = fs.createReadStream(filePath);
    
    fileStream.on('error', (error) => {
      console.error('File stream error:', error);
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: 'Error reading file'
        });
      }
    });

    fileStream.pipe(res);

  } catch (error) {
    console.error('File serving error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while serving file'
    });
  }
});

// @route   GET /api/uploads/events/:filename/download
// @desc    Force download of event files
// @access  Public for public events, Private for private events
router.get('/events/:filename/download', optionalAuth, async (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(uploadDir, 'events', filename);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'File not found'
      });
    }

    // Find the event that contains this file
    const event = await Event.findOne({
      'attachments.filename': filename
    });

    if (!event) {
      return res.status(404).json({
        success: false,
        message: 'File not associated with any event'
      });
    }

    // Check access permissions (same as above)
    if (!event.isPublic || event.status !== 'published') {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required to download this file'
        });
      }

      const canView = req.user.role === 'admin' || 
                     event.createdBy.toString() === req.user._id.toString() ||
                     event.organizer.toString() === req.user._id.toString();

      if (!canView) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to download this file'
        });
      }
    }

    // Get file info from event attachments
    const attachment = event.attachments.find(att => att.filename === filename);
    const originalName = attachment ? attachment.name : path.basename(filename);

    // Force download
    res.setHeader('Content-Disposition', `attachment; filename="${originalName}"`);
    res.setHeader('Content-Type', 'application/octet-stream');

    // Stream the file
    const fileStream = fs.createReadStream(filePath);
    
    fileStream.on('error', (error) => {
      console.error('File download error:', error);
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: 'Error downloading file'
        });
      }
    });

    fileStream.pipe(res);

  } catch (error) {
    console.error('File download error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while downloading file'
    });
  }
});

// @route   GET /api/uploads/events/:filename/info
// @desc    Get file information
// @access  Public for public events, Private for private events
router.get('/events/:filename/info', optionalAuth, async (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(uploadDir, 'events', filename);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'File not found'
      });
    }

    // Find the event that contains this file
    const event = await Event.findOne({
      'attachments.filename': filename
    });

    if (!event) {
      return res.status(404).json({
        success: false,
        message: 'File not associated with any event'
      });
    }

    // Check access permissions
    if (!event.isPublic || event.status !== 'published') {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required to access file information'
        });
      }

      const canView = req.user.role === 'admin' || 
                     event.createdBy.toString() === req.user._id.toString() ||
                     event.organizer.toString() === req.user._id.toString();

      if (!canView) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to access this file information'
        });
      }
    }

    // Get file stats and attachment info
    const stats = fs.statSync(filePath);
    const attachment = event.attachments.find(att => att.filename === filename);

    const fileInfo = {
      filename: filename,
      originalName: attachment ? attachment.name : path.basename(filename),
      size: stats.size,
      type: attachment ? attachment.type : 'application/octet-stream',
      lastModified: stats.mtime,
      eventId: event._id,
      eventTitle: event.title
    };

    res.json({
      success: true,
      data: fileInfo
    });

  } catch (error) {
    console.error('File info error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while getting file information'
    });
  }
});

module.exports = router;
