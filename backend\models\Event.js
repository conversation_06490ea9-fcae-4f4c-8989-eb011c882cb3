const mongoose = require("mongoose");

const eventSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "Event title is required"],
      trim: true,
      maxlength: [100, "Title cannot exceed 100 characters"],
    },
    description: {
      type: String,
      required: [true, "Event description is required"],
      trim: true,
      maxlength: [2000, "Description cannot exceed 2000 characters"],
    },
    startDate: {
      type: Date,
      required: [true, "Start date is required"],
    },
    endDate: {
      type: Date,
      required: [true, "End date is required"],
      validate: {
        validator: function (value) {
          return value >= this.startDate;
        },
        message: "End date must be after start date",
      },
    },
    startTime: {
      type: String,
      required: [true, "Start time is required"],
      match: [
        /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,
        "Please enter a valid time format (HH:MM)",
      ],
    },
    endTime: {
      type: String,
      required: [true, "End time is required"],
      match: [
        /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,
        "Please enter a valid time format (HH:MM)",
      ],
    },
    location: {
      name: {
        type: String,
        required: [true, "Location name is required"],
        trim: true,
        maxlength: [100, "Location name cannot exceed 100 characters"],
      },
      address: {
        type: String,
        trim: true,
        maxlength: [200, "Address cannot exceed 200 characters"],
      },
      room: {
        type: String,
        trim: true,
        maxlength: [50, "Room cannot exceed 50 characters"],
      },
    },
    category: {
      type: String,
      required: [true, "Event category is required"],
      enum: [
        "worship",
        "prayer",
        "bible-study",
        "youth",
        "children",
        "fellowship",
        "outreach",
        "conference",
        "wedding",
        "funeral",
        "baptism",
        "communion",
        "special-service",
        "community",
        "other",
      ],
      default: "worship",
    },
    priority: {
      type: String,
      enum: ["low", "medium", "high", "urgent"],
      default: "medium",
    },
    status: {
      type: String,
      enum: ["draft", "published", "cancelled", "completed"],
      default: "draft",
    },
    isRecurring: {
      type: Boolean,
      default: false,
    },
    recurrence: {
      pattern: {
        type: String,
        enum: ["daily", "weekly", "monthly", "yearly"],
        required: function () {
          return this.isRecurring;
        },
      },
      interval: {
        type: Number,
        min: 1,
        max: 52,
        default: 1,
        required: function () {
          return this.isRecurring;
        },
      },
      daysOfWeek: [
        {
          type: Number,
          min: 0,
          max: 6, // 0 = Sunday, 6 = Saturday
        },
      ],
      endDate: {
        type: Date,
        validate: {
          validator: function (value) {
            return !this.isRecurring || value > this.startDate;
          },
          message: "Recurrence end date must be after start date",
        },
      },
    },
    organizer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: [true, "Event organizer is required"],
    },
    pointOfContact: {
      name: {
        type: String,
        trim: true,
        maxlength: [100, "Contact name cannot exceed 100 characters"],
      },
      email: {
        type: String,
        trim: true,
        lowercase: true,
        match: [
          /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
          "Please enter a valid email address",
        ],
      },
      phone: {
        type: String,
        trim: true,
        maxlength: [20, "Phone number cannot exceed 20 characters"],
      },
    },
    attendees: {
      expected: {
        type: Number,
        min: 0,
        default: 0,
      },
      registered: {
        type: Number,
        min: 0,
        default: 0,
      },
    },
    resources: {
      equipment: [
        {
          type: String,
          trim: true,
        },
      ],
      materials: [
        {
          type: String,
          trim: true,
        },
      ],
      volunteers: [
        {
          name: {
            type: String,
            required: true,
            trim: true,
          },
          role: {
            type: String,
            required: true,
            trim: true,
          },
          contact: {
            type: String,
            trim: true,
          },
        },
      ],
    },
    images: [
      {
        url: String,
        caption: String,
        isPrimary: {
          type: Boolean,
          default: false,
        },
      },
    ],
    attachments: [
      {
        name: String,
        url: String,
        size: Number,
        type: String,
      },
    ],
    tags: [
      {
        type: String,
        trim: true,
        lowercase: true,
      },
    ],
    isPublic: {
      type: Boolean,
      default: true,
    },
    requiresRegistration: {
      type: Boolean,
      default: false,
    },
    maxAttendees: {
      type: Number,
      min: 0,
    },
    cost: {
      amount: {
        type: Number,
        min: 0,
        default: 0,
      },
      currency: {
        type: String,
        default: "USD",
      },
      isFree: {
        type: Boolean,
        default: true,
      },
    },
    contactInfo: {
      name: String,
      email: String,
      phone: String,
    },
    notes: {
      type: String,
      maxlength: [1000, "Notes cannot exceed 1000 characters"],
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    lastModifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Virtual for duration in minutes
eventSchema.virtual("duration").get(function () {
  const start = new Date(`1970-01-01T${this.startTime}:00`);
  const end = new Date(`1970-01-01T${this.endTime}:00`);
  return Math.round((end - start) / (1000 * 60));
});

// Virtual for full date range
eventSchema.virtual("dateRange").get(function () {
  if (this.startDate.toDateString() === this.endDate.toDateString()) {
    return this.startDate.toDateString();
  }
  return `${this.startDate.toDateString()} - ${this.endDate.toDateString()}`;
});

// Indexes for better query performance
eventSchema.index({ startDate: 1, endDate: 1 });
eventSchema.index({ category: 1 });
eventSchema.index({ status: 1 });
eventSchema.index({ isPublic: 1 });
eventSchema.index({ organizer: 1 });
eventSchema.index({ createdBy: 1 });
eventSchema.index({ tags: 1 });
eventSchema.index({ "location.name": 1 });

// Text index for search functionality
eventSchema.index({
  title: "text",
  description: "text",
  "location.name": "text",
  tags: "text",
});

// Pre-save middleware
eventSchema.pre("save", function (next) {
  // Set lastModifiedBy if document is being updated
  if (this.isModified() && !this.isNew) {
    this.lastModifiedBy = this.createdBy; // This should be set by the controller
  }
  next();
});

// Static method to find public events
eventSchema.statics.findPublic = function () {
  return this.find({ isPublic: true, status: "published" });
};

// Static method to find upcoming events
eventSchema.statics.findUpcoming = function () {
  return this.find({
    startDate: { $gte: new Date() },
    status: "published",
  }).sort({ startDate: 1 });
};

module.exports = mongoose.model("Event", eventSchema);
