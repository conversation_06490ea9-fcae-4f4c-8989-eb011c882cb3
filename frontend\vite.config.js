import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { VitePWA } from "vite-plugin-pwa";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    VitePWA({
      registerType: "autoUpdate",
      includeAssets: ["favicon.ico", "apple-touch-icon.png"],
      manifest: {
        name: "Grace Bible Fellowship - Event Management",
        short_name: "GBF Events",
        description:
          "Grace Bible Fellowship Church Event Management System - Stay connected with your church community",
        theme_color: "#0066cc",
        background_color: "#ffffff",
        display: "standalone",
        orientation: "portrait-primary",
        scope: "/",
        start_url: "/",
        categories: ["lifestyle", "social", "productivity"],
        lang: "en-US",
        dir: "ltr",
        icons: [
          {
            src: "/icons/icon-72x72.png",
            sizes: "72x72",
            type: "image/png",
            purpose: "any",
          },
          {
            src: "/icons/icon-96x96.png",
            sizes: "96x96",
            type: "image/png",
            purpose: "any",
          },
          {
            src: "/icons/icon-128x128.png",
            sizes: "128x128",
            type: "image/png",
            purpose: "any",
          },
          {
            src: "/icons/icon-144x144.png",
            sizes: "144x144",
            type: "image/png",
            purpose: "any",
          },
          {
            src: "/icons/icon-152x152.png",
            sizes: "152x152",
            type: "image/png",
            purpose: "any",
          },
          {
            src: "/icons/icon-192x192.png",
            sizes: "192x192",
            type: "image/png",
            purpose: "any",
          },
          {
            src: "/icons/icon-384x384.png",
            sizes: "384x384",
            type: "image/png",
            purpose: "any",
          },
          {
            src: "/icons/icon-512x512.png",
            sizes: "512x512",
            type: "image/png",
            purpose: "any",
          },
          {
            src: "/icons/maskable-icon-192x192.png",
            sizes: "192x192",
            type: "image/png",
            purpose: "maskable",
          },
          {
            src: "/icons/maskable-icon-512x512.png",
            sizes: "512x512",
            type: "image/png",
            purpose: "maskable",
          },
        ],
        screenshots: [
          {
            src: "/screenshots/mobile-home.png",
            sizes: "390x844",
            type: "image/png",
            form_factor: "narrow",
            label: "Home screen showing upcoming events",
          },
          {
            src: "/screenshots/mobile-calendar.png",
            sizes: "390x844",
            type: "image/png",
            form_factor: "narrow",
            label: "Calendar view with church events",
          },
          {
            src: "/screenshots/desktop-home.png",
            sizes: "1280x720",
            type: "image/png",
            form_factor: "wide",
            label: "Desktop home view",
          },
        ],
        shortcuts: [
          {
            name: "View Calendar",
            short_name: "Calendar",
            description: "View church events calendar",
            url: "/calendar",
            icons: [
              {
                src: "/icons/calendar-shortcut.png",
                sizes: "96x96",
                type: "image/png",
              },
            ],
          },
          {
            name: "Upcoming Events",
            short_name: "Events",
            description: "View upcoming church events",
            url: "/events",
            icons: [
              {
                src: "/icons/events-shortcut.png",
                sizes: "96x96",
                type: "image/png",
              },
            ],
          },
        ],
      },
      workbox: {
        globPatterns: ["**/*.{js,css,html,ico,png,svg}"],
      },
      devOptions: {
        enabled: true,
      },
    }),
  ],

  server: {
    port: 3000,
    host: "0.0.0.0",
    open: true,
    https: false, // Set to true for HTTPS testing
  },

  build: {
    outDir: "dist",
    sourcemap: true,
  },

  resolve: {
    dedupe: ["react", "react-dom"],
  },

  optimizeDeps: {
    include: [
      "react",
      "react-dom",
      "react-router-dom",
      "styled-components",
      "zustand",
      "axios",
      "react-toastify",
      "framer-motion",
      "lucide-react",
      "react-big-calendar",
      "react-datepicker",
      "date-fns",
      "moment",
    ],
  },
});
