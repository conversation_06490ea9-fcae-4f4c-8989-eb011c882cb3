import React, { useState } from "react";
import styled from "styled-components";
import { motion } from "framer-motion";
import { User, Mail, Phone, Lock, Save, Edit } from "lucide-react";
import { useAuth } from "../hooks/useAuth";

const ProfileContainer = styled.div`
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  /* Remove excessive padding - let Layout handle spacing */
  padding: 0;

  @media (min-width: ${({ theme }) => theme.breakpoints.lg}) {
    max-width: 900px;
  }
`;

const ProfileCard = styled(motion.div)`
  background: ${({ theme }) => theme.colors.background.primary};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  box-shadow: ${({ theme }) => theme.shadows.md};
  overflow: hidden;
`;

const ProfileHeader = styled.div`
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors.primary[600]} 0%,
    ${({ theme }) => theme.colors.primary[700]} 100%
  );
  color: white;
  padding: ${({ theme }) => theme.spacing[8]};
  text-align: center;
`;

const Avatar = styled.div`
  width: 80px;
  height: 80px;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  background: ${({ theme }) => theme.colors.primary[500]};
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto ${({ theme }) => theme.spacing[4]} auto;
  font-size: ${({ theme }) => theme.fontSizes["2xl"]};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
`;

const ProfileName = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes["2xl"]};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  margin: 0 0 ${({ theme }) => theme.spacing[2]} 0;
`;

const ProfileRole = styled.p`
  opacity: 0.9;
  margin: 0;
  font-size: ${({ theme }) => theme.fontSizes.base};
`;

const ProfileBody = styled.div`
  padding: ${({ theme }) => theme.spacing[8]};
`;

const Section = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[8]};

  &:last-child {
    margin-bottom: 0;
  }
`;

const SectionTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes.xl};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0 0 ${({ theme }) => theme.spacing[4]} 0;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[4]};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const Label = styled.label`
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const InputWrapper = styled.div`
  position: relative;
  display: flex;
  align-items: center;
`;

const InputIcon = styled.div`
  position: absolute;
  left: ${({ theme }) => theme.spacing[3]};
  color: ${({ theme }) => theme.colors.text.secondary};
  display: flex;
  align-items: center;
`;

const Input = styled.input`
  width: 100%;
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[3]}
    ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[10]};
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSizes.base};
  transition: all ${({ theme }) => theme.transitions.fast};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary[500]};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.primary[100]};
  }

  &:disabled {
    background: ${({ theme }) => theme.colors.background.tertiary};
    cursor: not-allowed;
  }
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[6]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  transition: all ${({ theme }) => theme.transitions.fast};

  &.primary {
    background: ${({ theme }) => theme.colors.primary[600]};
    color: white;

    &:hover:not(:disabled) {
      background: ${({ theme }) => theme.colors.primary[700]};
      transform: translateY(-1px);
      box-shadow: ${({ theme }) => theme.shadows.md};
    }
  }

  &.secondary {
    background: ${({ theme }) => theme.colors.background.secondary};
    color: ${({ theme }) => theme.colors.text.primary};
    border: 1px solid ${({ theme }) => theme.colors.border.medium};

    &:hover:not(:disabled) {
      background: ${({ theme }) => theme.colors.background.tertiary};
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[3]};
  justify-content: flex-end;
  margin-top: ${({ theme }) => theme.spacing[4]};
`;

const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors.error[600]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: ${({ theme }) => theme.spacing[1]};
`;

const SuccessMessage = styled.div`
  color: ${({ theme }) => theme.colors.success[600]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: ${({ theme }) => theme.spacing[1]};
`;

const ProfilePage = () => {
  const { user, updateProfile, changePassword, isLoading } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState({
    firstName: user?.firstName || "",
    lastName: user?.lastName || "",
    phone: user?.phone || "",
  });
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");

  const handleProfileChange = (e) => {
    const { name, value } = e.target;
    setProfileData((prev) => ({ ...prev, [name]: value }));
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData((prev) => ({ ...prev, [name]: value }));
  };

  const handleProfileSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setMessage("");

    try {
      await updateProfile(profileData);
      setMessage("Profile updated successfully!");
      setIsEditing(false);
    } catch (err) {
      setError("Failed to update profile. Please try again.");
    }
  };

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setMessage("");

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setError("New passwords do not match.");
      return;
    }

    if (passwordData.newPassword.length < 6) {
      setError("New password must be at least 6 characters long.");
      return;
    }

    try {
      await changePassword({
        currentPassword: passwordData.currentPassword,
        newPassword: passwordData.newPassword,
      });
      setMessage("Password changed successfully!");
      setPasswordData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
    } catch (err) {
      setError(
        "Failed to change password. Please check your current password."
      );
    }
  };

  const getInitials = (firstName, lastName) => {
    return `${firstName?.charAt(0) || ""}${
      lastName?.charAt(0) || ""
    }`.toUpperCase();
  };

  return (
    <ProfileContainer>
      <ProfileCard
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <ProfileHeader>
          <Avatar>{getInitials(user?.firstName, user?.lastName)}</Avatar>
          <ProfileName>
            {user?.firstName} {user?.lastName}
          </ProfileName>
          <ProfileRole>
            {user?.role === "admin" ? "Administrator" : "Editor"}
          </ProfileRole>
        </ProfileHeader>

        <ProfileBody>
          <Section>
            <SectionTitle>
              <User size={20} />
              Profile Information
            </SectionTitle>

            <Form onSubmit={handleProfileSubmit}>
              <FormGroup>
                <Label>
                  <User size={16} />
                  First Name
                </Label>
                <InputWrapper>
                  <InputIcon>
                    <User size={18} />
                  </InputIcon>
                  <Input
                    type="text"
                    name="firstName"
                    value={profileData.firstName}
                    onChange={handleProfileChange}
                    disabled={!isEditing}
                    required
                  />
                </InputWrapper>
              </FormGroup>

              <FormGroup>
                <Label>
                  <User size={16} />
                  Last Name
                </Label>
                <InputWrapper>
                  <InputIcon>
                    <User size={18} />
                  </InputIcon>
                  <Input
                    type="text"
                    name="lastName"
                    value={profileData.lastName}
                    onChange={handleProfileChange}
                    disabled={!isEditing}
                    required
                  />
                </InputWrapper>
              </FormGroup>

              <FormGroup>
                <Label>
                  <Mail size={16} />
                  Email Address
                </Label>
                <InputWrapper>
                  <InputIcon>
                    <Mail size={18} />
                  </InputIcon>
                  <Input type="email" value={user?.email || ""} disabled />
                </InputWrapper>
              </FormGroup>

              <FormGroup>
                <Label>
                  <Phone size={16} />
                  Phone Number
                </Label>
                <InputWrapper>
                  <InputIcon>
                    <Phone size={18} />
                  </InputIcon>
                  <Input
                    type="tel"
                    name="phone"
                    value={profileData.phone}
                    onChange={handleProfileChange}
                    disabled={!isEditing}
                    placeholder="Enter phone number"
                  />
                </InputWrapper>
              </FormGroup>

              <ButtonGroup>
                {isEditing ? (
                  <>
                    <Button
                      type="button"
                      className="secondary"
                      onClick={() => setIsEditing(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="primary"
                      disabled={isLoading}
                    >
                      <Save size={16} />
                      Save Changes
                    </Button>
                  </>
                ) : (
                  <Button
                    type="button"
                    className="primary"
                    onClick={() => setIsEditing(true)}
                  >
                    <Edit size={16} />
                    Edit Profile
                  </Button>
                )}
              </ButtonGroup>
            </Form>
          </Section>

          <Section>
            <SectionTitle>
              <Lock size={20} />
              Change Password
            </SectionTitle>

            <Form onSubmit={handlePasswordSubmit}>
              <FormGroup>
                <Label>Current Password</Label>
                <InputWrapper>
                  <InputIcon>
                    <Lock size={18} />
                  </InputIcon>
                  <Input
                    type="password"
                    name="currentPassword"
                    value={passwordData.currentPassword}
                    onChange={handlePasswordChange}
                    placeholder="Enter current password"
                    required
                  />
                </InputWrapper>
              </FormGroup>

              <FormGroup>
                <Label>New Password</Label>
                <InputWrapper>
                  <InputIcon>
                    <Lock size={18} />
                  </InputIcon>
                  <Input
                    type="password"
                    name="newPassword"
                    value={passwordData.newPassword}
                    onChange={handlePasswordChange}
                    placeholder="Enter new password"
                    required
                  />
                </InputWrapper>
              </FormGroup>

              <FormGroup>
                <Label>Confirm New Password</Label>
                <InputWrapper>
                  <InputIcon>
                    <Lock size={18} />
                  </InputIcon>
                  <Input
                    type="password"
                    name="confirmPassword"
                    value={passwordData.confirmPassword}
                    onChange={handlePasswordChange}
                    placeholder="Confirm new password"
                    required
                  />
                </InputWrapper>
              </FormGroup>

              <ButtonGroup>
                <Button type="submit" className="primary" disabled={isLoading}>
                  <Lock size={16} />
                  Change Password
                </Button>
              </ButtonGroup>
            </Form>
          </Section>

          {error && <ErrorMessage>{error}</ErrorMessage>}
          {message && <SuccessMessage>{message}</SuccessMessage>}
        </ProfileBody>
      </ProfileCard>
    </ProfileContainer>
  );
};

export default ProfilePage;
