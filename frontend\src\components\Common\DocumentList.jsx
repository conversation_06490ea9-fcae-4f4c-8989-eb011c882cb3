import React from "react";
import styled from "styled-components";
import { motion } from "framer-motion";
import {
  File,
  FileText,
  Image,
  Download,
  Paperclip,
  ExternalLink,
  X,
} from "lucide-react";

const DocumentContainer = styled.div`
  width: 100%;
`;

const DocumentHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  margin-bottom: ${({ theme }) => theme.spacing[3]};
`;

const DocumentTitle = styled.h4`
  margin: 0;
  color: ${({ theme }) => theme.colors.text};
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
`;

const DocumentCount = styled.span`
  background: ${({ theme }) => `${theme.colors.primary}15`};
  color: ${({ theme }) => theme.colors.primary};
  padding: ${({ theme }) => `${theme.spacing[1]} ${theme.spacing[2]}`};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: 500;
`;

const DocumentList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const DocumentItem = styled(motion.div)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[3]};
  background: ${({ theme }) => theme.colors.surface};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme }) => `${theme.colors.primary}05`};
    border-color: ${({ theme }) => `${theme.colors.primary}30`};
  }
`;

const DocumentIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background: ${({ theme, fileType }) => {
    if (fileType?.startsWith("image/")) return `${theme.colors.success}15`;
    if (fileType?.includes("pdf")) return `${theme.colors.error}15`;
    return `${theme.colors.primary}15`;
  }};
  color: ${({ theme, fileType }) => {
    if (fileType?.startsWith("image/")) return theme.colors.success;
    if (fileType?.includes("pdf")) return theme.colors.error;
    return theme.colors.primary;
  }};
`;

const DocumentInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const DocumentName = styled.p`
  margin: 0 0 ${({ theme }) => theme.spacing[1]} 0;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const DocumentMeta = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const DocumentSize = styled.span``;

const DocumentType = styled.span`
  text-transform: uppercase;
`;

const DocumentActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  background: ${({ theme }) => `${theme.colors.primary}15`};
  color: ${({ theme }) => theme.colors.primary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme }) => `${theme.colors.primary}25`};
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing[6]};
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const getFileIcon = (type) => {
  if (type?.startsWith("image/")) return Image;
  if (type?.includes("pdf")) return FileText;
  return File;
};

const getFileExtension = (filename) => {
  return filename?.split(".").pop()?.toUpperCase() || "FILE";
};

const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const DocumentListComponent = ({
  documents = [],
  title = "Documents & Attachments",
  showCount = true,
  compact = false,
  onDelete = null,
  allowDelete = false,
}) => {
  if (!documents || documents.length === 0) {
    return (
      <DocumentContainer>
        <DocumentHeader>
          <Paperclip size={20} />
          <DocumentTitle>{title}</DocumentTitle>
        </DocumentHeader>
        <EmptyState>No documents attached to this event.</EmptyState>
      </DocumentContainer>
    );
  }

  const handleDownload = (document) => {
    if (document.url) {
      // Create a temporary link to trigger download
      const link = document.createElement("a");
      link.href = document.url;
      link.download = document.name || "document";
      link.target = "_blank";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleView = (document) => {
    if (document.url) {
      window.open(document.url, "_blank");
    }
  };

  const handleDelete = (document, index) => {
    if (window.confirm(`Are you sure you want to delete "${document.name}"?`)) {
      if (onDelete) {
        onDelete(document, index);
      }
    }
  };

  return (
    <DocumentContainer>
      <DocumentHeader>
        <Paperclip size={20} />
        <DocumentTitle>{title}</DocumentTitle>
        {showCount && (
          <DocumentCount>
            {documents.length} {documents.length === 1 ? "file" : "files"}
          </DocumentCount>
        )}
      </DocumentHeader>

      <DocumentList>
        {documents.map((document, index) => {
          const IconComponent = getFileIcon(document.type);
          const fileExtension = getFileExtension(document.name);

          return (
            <DocumentItem
              key={`${document.name}-${index}`}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.2, delay: index * 0.1 }}
            >
              <DocumentIcon fileType={document.type}>
                <IconComponent size={compact ? 16 : 20} />
              </DocumentIcon>

              <DocumentInfo>
                <DocumentName>{document.name}</DocumentName>
                <DocumentMeta>
                  <DocumentType>{fileExtension}</DocumentType>
                  {document.size && (
                    <>
                      <span>•</span>
                      <DocumentSize>
                        {formatFileSize(document.size)}
                      </DocumentSize>
                    </>
                  )}
                </DocumentMeta>
              </DocumentInfo>

              <DocumentActions>
                <ActionButton
                  onClick={() => handleView(document)}
                  title="View document"
                >
                  <ExternalLink size={14} />
                </ActionButton>
                <ActionButton
                  onClick={() => handleDownload(document)}
                  title="Download document"
                >
                  <Download size={14} />
                </ActionButton>
                {allowDelete && (
                  <ActionButton
                    onClick={() => handleDelete(document, index)}
                    title="Delete document"
                    variant="danger"
                  >
                    <X size={14} />
                  </ActionButton>
                )}
              </DocumentActions>
            </DocumentItem>
          );
        })}
      </DocumentList>
    </DocumentContainer>
  );
};

export default DocumentListComponent;
