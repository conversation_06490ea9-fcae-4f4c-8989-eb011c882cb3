{"name": "church-event-management-backend", "version": "1.0.0", "description": "Backend API for Grace Bible Fellowship System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed:admin": "node scripts/seedAdmin.js", "seed:events": "node scripts/seedEvents.js", "seed:all": "npm run seed:admin && npm run seed:events", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["church", "events", "management", "api"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.18.2", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.1", "multer": "^2.0.2", "path-to-regexp": "^8.2.0", "pdfkit": "^0.15.0", "docx": "^8.5.0", "moment": "^2.30.1"}, "devDependencies": {"nodemon": "^3.1.10"}}