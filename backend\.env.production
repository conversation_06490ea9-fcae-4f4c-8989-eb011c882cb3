NODE_ENV=production
PORT=5000

# Production Database - Update with your production MongoDB connection string
MONGODB_URI=mongodb+srv://username:<EMAIL>/church-events?retryWrites=true&w=majority

# JWT Configuration - Use a strong, unique secret for production
JWT_SECRET=your-super-secure-production-jwt-secret-key-2024-change-this
JWT_EXPIRE=7d

# Frontend URLs - Update with your actual production domains
FRONTEND_URL=https://your-production-domain.com
PRODUCTION_FRONTEND_URL=https://your-production-domain.com

# Additional production frontend URLs (if you have multiple)
# STAGING_FRONTEND_URL=https://staging.your-domain.com
# CDN_URL=https://cdn.your-domain.com

# Email configuration for production
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-specific-password

# File upload configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Security settings for production
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=5

# Optional: Additional security headers
SECURE_COOKIES=true
TRUST_PROXY=true
