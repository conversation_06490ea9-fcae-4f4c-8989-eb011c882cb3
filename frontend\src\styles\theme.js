export const theme = {
  colors: {
    // Primary colors - Warm Church Blue (Deep spiritual blue with warmth)
    primary: {
      50: "#f0f7ff",
      100: "#e0efff",
      200: "#bae0ff",
      300: "#7cc8ff",
      400: "#36acff",
      500: "#0c8ce9",
      600: "#0066cc",
      700: "#0052a3",
      800: "#004785",
      900: "#003d6b",
    },

    // Secondary colors - Warm Gold (Representing divine light and warmth)
    secondary: {
      50: "#fffdf7",
      100: "#fef9e7",
      200: "#fef0c7",
      300: "#fde047",
      400: "#facc15",
      500: "#eab308",
      600: "#ca8a04",
      700: "#a16207",
      800: "#854d0e",
      900: "#713f12",
    },

    // Accent colors - Soft Purple (Spiritual depth and reverence)
    accent: {
      50: "#faf7ff",
      100: "#f3edff",
      200: "#e9d8ff",
      300: "#d8b4fe",
      400: "#c084fc",
      500: "#a855f7",
      600: "#9333ea",
      700: "#7c3aed",
      800: "#6b21a8",
      900: "#581c87",
    },

    // Neutral colors - Warmer grays
    gray: {
      50: "#fafaf9",
      100: "#f5f5f4",
      200: "#e7e5e4",
      300: "#d6d3d1",
      400: "#a8a29e",
      500: "#78716c",
      600: "#57534e",
      700: "#44403c",
      800: "#292524",
      900: "#1c1917",
    },

    // Status colors
    success: {
      50: "#ecfdf5",
      100: "#d1fae5",
      200: "#a7f3d0",
      300: "#6ee7b7",
      400: "#34d399",
      500: "#10b981",
      600: "#059669",
      700: "#047857",
      800: "#065f46",
      900: "#064e3b",
    },

    error: {
      50: "#fef2f2",
      100: "#fee2e2",
      200: "#fecaca",
      300: "#fca5a5",
      400: "#f87171",
      500: "#ef4444",
      600: "#dc2626",
      700: "#b91c1c",
      800: "#991b1b",
      900: "#7f1d1d",
    },

    warning: {
      50: "#fffbeb",
      100: "#fef3c7",
      200: "#fde68a",
      300: "#fcd34d",
      400: "#fbbf24",
      500: "#f59e0b",
      600: "#d97706",
      700: "#b45309",
      800: "#92400e",
      900: "#78350f",
    },

    info: {
      50: "#eff6ff",
      100: "#dbeafe",
      200: "#bfdbfe",
      300: "#93c5fd",
      400: "#60a5fa",
      500: "#3b82f6",
      600: "#2563eb",
      700: "#1d4ed8",
      800: "#1e40af",
      900: "#1e3a8a",
    },

    // Background colors - Warmer, more welcoming
    background: {
      primary: "#ffffff",
      secondary: "#fafaf9", // Warmer white
      tertiary: "#f5f5f4", // Soft warm gray
      accent: "#f0f7ff", // Light blue tint for special sections
      card: "#ffffff", // Card backgrounds
      overlay: "rgba(0, 0, 0, 0.5)", // Modal overlays
    },

    // Text colors - Better hierarchy and warmth
    text: {
      primary: "#1c1917", // Warmer black
      secondary: "#44403c", // Medium warm gray
      tertiary: "#78716c", // Light warm gray
      inverse: "#ffffff", // White text
      accent: "#0066cc", // Primary blue for links/accents
      muted: "#a8a29e", // Very light text
    },

    // Border colors - Softer, warmer borders
    border: {
      light: "#e7e5e4", // Very light warm border
      medium: "#d6d3d1", // Medium warm border
      dark: "#a8a29e", // Darker warm border
      accent: "#bae0ff", // Primary colored border
    },

    // Church-specific semantic colors
    church: {
      worship: "#7c3aed", // Purple for worship events
      fellowship: "#eab308", // Gold for fellowship
      service: "#0066cc", // Blue for service events
      prayer: "#a855f7", // Light purple for prayer
      study: "#059669", // Green for Bible study
      youth: "#dc2626", // Red for youth events
      children: "#ea580c", // Orange for children
      music: "#7c2d12", // Brown for music ministry
    },
  },

  // Typography - Enhanced for mobile readability
  fonts: {
    primary:
      '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    heading:
      '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    mono: '"Fira Code", "Monaco", "Cascadia Code", "Segoe UI Mono", "Roboto Mono", monospace',
    display:
      '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  },

  fontSizes: {
    xs: "0.75rem", // 12px - Small labels
    sm: "0.875rem", // 14px - Secondary text
    base: "1rem", // 16px - Body text
    lg: "1.125rem", // 18px - Large body text
    xl: "1.25rem", // 20px - Small headings
    "2xl": "1.5rem", // 24px - Medium headings
    "3xl": "1.875rem", // 30px - Large headings
    "4xl": "2.25rem", // 36px - Extra large headings
    "5xl": "3rem", // 48px - Display text
    "6xl": "3.75rem", // 60px - Hero text
    // Mobile-optimized sizes
    "mobile-xs": "0.8125rem", // 13px
    "mobile-sm": "0.9375rem", // 15px
    "mobile-base": "1.0625rem", // 17px
    "mobile-lg": "1.1875rem", // 19px
  },

  fontWeights: {
    thin: 100,
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
    black: 900,
  },

  lineHeights: {
    none: 1,
    tight: 1.25,
    snug: 1.375,
    normal: 1.5,
    relaxed: 1.625,
    loose: 2,
  },

  // Spacing
  spacing: {
    0: "0",
    1: "0.25rem", // 4px
    2: "0.5rem", // 8px
    3: "0.75rem", // 12px
    4: "1rem", // 16px
    5: "1.25rem", // 20px
    6: "1.5rem", // 24px
    8: "2rem", // 32px
    10: "2.5rem", // 40px
    12: "3rem", // 48px
    16: "4rem", // 64px
    20: "5rem", // 80px
    24: "6rem", // 96px
    32: "8rem", // 128px
    40: "10rem", // 160px
    48: "12rem", // 192px
    56: "14rem", // 224px
    64: "16rem", // 256px
  },

  // Border radius
  borderRadius: {
    none: "0",
    sm: "0.125rem", // 2px
    base: "0.25rem", // 4px
    md: "0.375rem", // 6px
    lg: "0.5rem", // 8px
    xl: "0.75rem", // 12px
    "2xl": "1rem", // 16px
    "3xl": "1.5rem", // 24px
    full: "9999px",
  },

  // Shadows
  shadows: {
    sm: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
    base: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
    md: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
    lg: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
    xl: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
    "2xl": "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
    inner: "inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)",
    none: "none",
  },

  // Breakpoints for responsive design
  breakpoints: {
    sm: "640px",
    md: "768px",
    lg: "1024px",
    xl: "1280px",
    "2xl": "1536px",
  },

  // Z-index scale
  zIndex: {
    auto: "auto",
    0: 0,
    10: 10,
    20: 20,
    30: 30,
    40: 40,
    50: 50,
    modal: 1000,
    popover: 1010,
    tooltip: 1020,
    dropdown: 1030,
  },

  // Transitions
  transitions: {
    fast: "150ms ease-in-out",
    base: "200ms ease-in-out",
    slow: "300ms ease-in-out",
  },
};
