import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import styled from "styled-components";
import { motion } from "framer-motion";
import { toast } from "react-toastify";
import {
  ArrowLeft,
  Calendar,
  Clock,
  MapPin,
  Users,
  Edit,
  Trash2,
  User,
  Mail,
  Phone,
  Repeat,
} from "lucide-react";
import { useEventsStore } from "../store";
import { useAuth } from "../hooks/useAuth";
import EventForm from "../components/Events/EventForm";
import DocumentList from "../components/Common/DocumentList";
import { formatDate, formatTime, getDateRange } from "../utils/dateUtils";
import { EVENT_CATEGORIES } from "../utils/constants";

const DetailContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing[6]} 0;
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  color: ${({ theme }) => theme.colors.primary[600]};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  transition: color ${({ theme }) => theme.transitions.fast};

  &:hover {
    color: ${({ theme }) => theme.colors.primary[700]};
  }
`;

const EventHeader = styled(motion.div)`
  background: ${({ theme }) => theme.colors.background.primary};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  box-shadow: ${({ theme }) => theme.shadows.md};
  padding: ${({ theme }) => theme.spacing[8]};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  border-left: 4px solid ${({ theme }) => theme.colors.primary[500]};
`;

const HeaderTop = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  gap: ${({ theme }) => theme.spacing[4]};
`;

const EventTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes["3xl"]};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
  line-height: ${({ theme }) => theme.lineHeights.tight};

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    font-size: ${({ theme }) => theme.fontSizes["2xl"]};
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[4]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  transition: all ${({ theme }) => theme.transitions.fast};

  &.edit {
    background: ${({ theme }) => theme.colors.info[100]};
    color: ${({ theme }) => theme.colors.info[700]};

    &:hover {
      background: ${({ theme }) => theme.colors.info[200]};
    }
  }

  &.delete {
    background: ${({ theme }) => theme.colors.error[100]};
    color: ${({ theme }) => theme.colors.error[700]};

    &:hover {
      background: ${({ theme }) => theme.colors.error[200]};
    }
  }
`;

const EventMeta = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: ${({ theme }) => theme.fontSizes.lg};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const EventContent = styled.div`
  display: grid;
  gap: ${({ theme }) => theme.spacing[6]};
`;

const Section = styled(motion.div)`
  background: ${({ theme }) => theme.colors.background.primary};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  padding: ${({ theme }) => theme.spacing[6]};
`;

const SectionTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes.xl};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0 0 ${({ theme }) => theme.spacing[4]} 0;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const DetailGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing[4]};
`;

const DetailItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[3]};
  background: ${({ theme }) => theme.colors.background.secondary};
  border-radius: ${({ theme }) => theme.borderRadius.md};
`;

const DetailIcon = styled.div`
  color: ${({ theme }) => theme.colors.primary[500]};
  display: flex;
  align-items: center;
`;

const DetailContent = styled.div`
  flex: 1;
`;

const DetailLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.text.secondary};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: ${({ theme }) => theme.spacing[1]};
`;

const DetailValue = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.base};
  color: ${({ theme }) => theme.colors.text.primary};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
`;

const Description = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: ${({ theme }) => theme.fontSizes.base};
  line-height: ${({ theme }) => theme.lineHeights.relaxed};
  margin: 0;
  white-space: pre-wrap;
`;

const LoadingState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing[12]};
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const ErrorState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing[12]};
  color: ${({ theme }) => theme.colors.error[600]};
`;

const EventDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentEvent, isLoading, fetchEvent, updateEvent, deleteEvent } =
    useEventsStore();
  const { isAuthenticated, hasPermission } = useAuth();
  const [showEventForm, setShowEventForm] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadEvent = async () => {
      try {
        await fetchEvent(id);
      } catch (err) {
        setError("Failed to load event");
      }
    };

    loadEvent();
  }, [id, fetchEvent]);

  const handleBack = () => {
    navigate(-1);
  };

  const handleEdit = () => {
    setShowEventForm(true);
  };

  const handleDelete = async () => {
    if (
      window.confirm(
        "Are you sure you want to delete this event? This action cannot be undone."
      )
    ) {
      const loadingToast = toast.loading("Deleting event...");
      try {
        await deleteEvent(id);
        toast.dismiss(loadingToast);
        toast.success("Event deleted successfully! 🗑️");
        navigate("/events");
      } catch (error) {
        toast.dismiss(loadingToast);
        console.error("Failed to delete event:", error);
        const errorMessage =
          error.response?.data?.message || "Failed to delete event";
        toast.error(`Delete failed: ${errorMessage}`);
      }
    }
  };

  const handleFormSubmit = async (formData) => {
    try {
      await updateEvent(id, formData);
      setShowEventForm(false);
    } catch (error) {
      console.error("Failed to update event:", error);
      alert("Failed to update event. Please try again.");
    }
  };

  const getCategoryLabel = (categoryValue) => {
    const category = EVENT_CATEGORIES.find(
      (cat) => cat.value === categoryValue
    );
    return category ? category.label : categoryValue;
  };

  const canEdit = isAuthenticated && hasPermission("canEditEvents");
  const canDelete = isAuthenticated && hasPermission("canDeleteEvents");

  if (isLoading) {
    return (
      <DetailContainer>
        <LoadingState>Loading event details...</LoadingState>
      </DetailContainer>
    );
  }

  if (error || !currentEvent) {
    return (
      <DetailContainer>
        <ErrorState>
          <h3>Event not found</h3>
          <p>The event you're looking for doesn't exist or has been removed.</p>
          <button onClick={handleBack}>Go Back</button>
        </ErrorState>
      </DetailContainer>
    );
  }

  return (
    <DetailContainer>
      <BackButton onClick={handleBack}>
        <ArrowLeft size={20} />
        Back to Events
      </BackButton>

      <EventHeader
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <HeaderTop>
          <div>
            <EventTitle>{currentEvent.title}</EventTitle>
            <EventMeta>
              <Calendar size={20} />
              {getDateRange(currentEvent.startDate, currentEvent.endDate)}
            </EventMeta>
          </div>

          {isAuthenticated && (
            <ActionButtons>
              {canEdit && (
                <ActionButton className="edit" onClick={handleEdit}>
                  <Edit size={16} />
                  Edit
                </ActionButton>
              )}
              {canDelete && (
                <ActionButton className="delete" onClick={handleDelete}>
                  <Trash2 size={16} />
                  Delete
                </ActionButton>
              )}
            </ActionButtons>
          )}
        </HeaderTop>
      </EventHeader>

      <EventContent>
        <Section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <SectionTitle>
            <Clock size={20} />
            Event Details
          </SectionTitle>
          <DetailGrid>
            <DetailItem>
              <DetailIcon>
                <Clock size={18} />
              </DetailIcon>
              <DetailContent>
                <DetailLabel>Time</DetailLabel>
                <DetailValue>
                  {formatTime(currentEvent.startTime)} -{" "}
                  {formatTime(currentEvent.endTime)}
                </DetailValue>
              </DetailContent>
            </DetailItem>

            {currentEvent.location?.name && (
              <DetailItem>
                <DetailIcon>
                  <MapPin size={18} />
                </DetailIcon>
                <DetailContent>
                  <DetailLabel>Location</DetailLabel>
                  <DetailValue>{currentEvent.location.name}</DetailValue>
                </DetailContent>
              </DetailItem>
            )}

            {(currentEvent.pointOfContact?.name ||
              currentEvent.pointOfContact?.email ||
              currentEvent.pointOfContact?.phone) && (
              <DetailItem>
                <DetailIcon>
                  <User size={18} />
                </DetailIcon>
                <DetailContent>
                  <DetailLabel>Point of Contact</DetailLabel>
                  <DetailValue>
                    {currentEvent.pointOfContact.name && (
                      <div>{currentEvent.pointOfContact.name}</div>
                    )}
                    {currentEvent.pointOfContact.email && (
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                          fontSize: "0.9em",
                          color: "#666",
                          marginTop: "2px",
                        }}
                      >
                        <Mail size={14} />
                        {currentEvent.pointOfContact.email}
                      </div>
                    )}
                    {currentEvent.pointOfContact.phone && (
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                          fontSize: "0.9em",
                          color: "#666",
                          marginTop: "2px",
                        }}
                      >
                        <Phone size={14} />
                        {currentEvent.pointOfContact.phone}
                      </div>
                    )}
                  </DetailValue>
                </DetailContent>
              </DetailItem>
            )}

            <DetailItem>
              <DetailIcon>
                <Users size={18} />
              </DetailIcon>
              <DetailContent>
                <DetailLabel>Category</DetailLabel>
                <DetailValue>
                  {getCategoryLabel(currentEvent.category)}
                </DetailValue>
              </DetailContent>
            </DetailItem>

            {currentEvent.attendees?.expected > 0 && (
              <DetailItem>
                <DetailIcon>
                  <Users size={18} />
                </DetailIcon>
                <DetailContent>
                  <DetailLabel>Expected Attendees</DetailLabel>
                  <DetailValue>{currentEvent.attendees.expected}</DetailValue>
                </DetailContent>
              </DetailItem>
            )}

            {currentEvent.isRecurring && (
              <DetailItem>
                <DetailIcon>
                  <Repeat size={18} />
                </DetailIcon>
                <DetailContent>
                  <DetailLabel>Recurrence</DetailLabel>
                  <DetailValue>
                    {currentEvent.recurrence?.pattern && (
                      <div>
                        Every {currentEvent.recurrence.interval}{" "}
                        {currentEvent.recurrence.pattern}
                        {currentEvent.recurrence.interval > 1 ? "s" : ""}
                        {currentEvent.recurrence.pattern === "weekly" &&
                          currentEvent.recurrence.daysOfWeek?.length > 0 && (
                            <div
                              style={{
                                fontSize: "0.9em",
                                color: "#666",
                                marginTop: "2px",
                              }}
                            >
                              on{" "}
                              {currentEvent.recurrence.daysOfWeek
                                .map(
                                  (day) =>
                                    [
                                      "Sun",
                                      "Mon",
                                      "Tue",
                                      "Wed",
                                      "Thu",
                                      "Fri",
                                      "Sat",
                                    ][day]
                                )
                                .join(", ")}
                            </div>
                          )}
                        {currentEvent.recurrence.endDate && (
                          <div
                            style={{
                              fontSize: "0.9em",
                              color: "#666",
                              marginTop: "2px",
                            }}
                          >
                            Until{" "}
                            {new Date(
                              currentEvent.recurrence.endDate
                            ).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    )}
                  </DetailValue>
                </DetailContent>
              </DetailItem>
            )}
          </DetailGrid>
        </Section>

        {currentEvent.description && (
          <Section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <SectionTitle>Description</SectionTitle>
            <Description>{currentEvent.description}</Description>
          </Section>
        )}

        {currentEvent.attachments && currentEvent.attachments.length > 0 && (
          <Section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <DocumentList documents={currentEvent.attachments} />
          </Section>
        )}
      </EventContent>

      <EventForm
        event={currentEvent}
        isOpen={showEventForm}
        onClose={() => setShowEventForm(false)}
        onSubmit={handleFormSubmit}
        isLoading={isLoading}
      />
    </DetailContainer>
  );
};

export default EventDetailPage;
