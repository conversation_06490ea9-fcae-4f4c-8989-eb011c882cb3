const multer = require("multer");
const path = require("path");
const fs = require("fs");

// Ensure upload directory exists
const uploadDir = process.env.UPLOAD_PATH || "./uploads";
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Configure storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const eventUploadsDir = path.join(uploadDir, "events");
    if (!fs.existsSync(eventUploadsDir)) {
      fs.mkdirSync(eventUploadsDir, { recursive: true });
    }
    cb(null, eventUploadsDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename with timestamp and random string
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext);
    // Sanitize filename
    const sanitizedName = name.replace(/[^a-zA-Z0-9-_]/g, "_");
    cb(null, `${sanitizedName}-${uniqueSuffix}${ext}`);
  },
});

// File filter for allowed file types with enhanced security
const fileFilter = (req, file, cb) => {
  // Allowed file types for documents
  const allowedTypes = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.ms-powerpoint",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    "text/plain",
    "text/csv",
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
  ];

  // Allowed file extensions as backup check
  const allowedExtensions = [
    ".pdf",
    ".doc",
    ".docx",
    ".xls",
    ".xlsx",
    ".ppt",
    ".pptx",
    ".txt",
    ".csv",
    ".jpg",
    ".jpeg",
    ".png",
    ".gif",
    ".webp",
  ];

  // Dangerous file extensions to explicitly block
  const dangerousExtensions = [
    ".exe",
    ".bat",
    ".cmd",
    ".com",
    ".pif",
    ".scr",
    ".vbs",
    ".js",
    ".jar",
    ".php",
    ".asp",
    ".aspx",
    ".jsp",
    ".sh",
    ".ps1",
    ".py",
    ".rb",
    ".pl",
  ];

  const fileExtension = path.extname(file.originalname).toLowerCase();
  const fileName = file.originalname.toLowerCase();

  // Security checks
  if (dangerousExtensions.includes(fileExtension)) {
    return cb(
      new Error("File type is not allowed for security reasons"),
      false
    );
  }

  // Check for double extensions (e.g., file.pdf.exe)
  const allExtensions = fileName.match(/\.[a-z0-9]+/g) || [];
  if (allExtensions.length > 1) {
    const hasDangerousExtension = allExtensions.some((ext) =>
      dangerousExtensions.includes(ext)
    );
    if (hasDangerousExtension) {
      return cb(
        new Error("File with multiple extensions is not allowed"),
        false
      );
    }
  }

  // Check filename for suspicious patterns
  if (
    fileName.includes("..") ||
    fileName.includes("/") ||
    fileName.includes("\\")
  ) {
    return cb(new Error("Invalid filename"), false);
  }

  // Validate file type and extension match
  if (
    allowedTypes.includes(file.mimetype) &&
    allowedExtensions.includes(fileExtension)
  ) {
    cb(null, true);
  } else {
    cb(
      new Error(
        `File type not allowed. Allowed types: ${allowedExtensions.join(", ")}`
      ),
      false
    );
  }
};

// Configure multer
const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024, // 5MB default
    files: 10, // Maximum 10 files per upload
  },
  fileFilter: fileFilter,
});

// Middleware for handling upload errors
const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === "LIMIT_FILE_SIZE") {
      return res.status(400).json({
        success: false,
        message: "File too large. Maximum size is 5MB.",
      });
    }
    if (error.code === "LIMIT_FILE_COUNT") {
      return res.status(400).json({
        success: false,
        message: "Too many files. Maximum 10 files allowed.",
      });
    }
    if (error.code === "LIMIT_UNEXPECTED_FILE") {
      return res.status(400).json({
        success: false,
        message: "Unexpected field name for file upload.",
      });
    }
  }

  if (error.message.includes("File type not allowed")) {
    return res.status(400).json({
      success: false,
      message: error.message,
    });
  }

  return res.status(500).json({
    success: false,
    message: "File upload error occurred.",
  });
};

// Helper function to delete files
const deleteFile = (filePath) => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      return true;
    }
  } catch (error) {
    console.error("Error deleting file:", error);
  }
  return false;
};

// Helper function to get file info
const getFileInfo = (file) => {
  return {
    name: file.originalname,
    filename: file.filename,
    url: `/api/uploads/events/${file.filename}`,
    size: file.size,
    type: file.mimetype,
  };
};

module.exports = {
  upload,
  handleUploadError,
  deleteFile,
  getFileInfo,
  uploadDir,
};
