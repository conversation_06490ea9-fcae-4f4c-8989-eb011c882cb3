import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { motion } from "framer-motion";
import { Search, Filter, Plus, Download } from "lucide-react";
import { useEventsStore } from "../store";
import { useAuth } from "../hooks/useAuth";
import EventCard from "../components/Events/EventCard";
import EventModal from "../components/Events/EventModal";
import EventForm from "../components/Events/EventForm";
import ExportModal from "../components/Export/ExportModal";
import { EVENT_CATEGORIES, EVENT_STATUSES } from "../utils/constants";

const EventsContainer = styled.div`
  width: 100%;
  /* Remove excessive padding - let Layout handle spacing */
  padding: 0;
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing[4]};
`;

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes["3xl"]};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    font-size: ${({ theme }) => theme.fontSizes["2xl"]};
  }
`;

const HeaderActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[3]};
  flex-wrap: wrap;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[4]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  transition: all ${({ theme }) => theme.transitions.fast};

  &.primary {
    background: ${({ theme }) => theme.colors.primary[600]};
    color: white;

    &:hover {
      background: ${({ theme }) => theme.colors.primary[700]};
      transform: translateY(-1px);
      box-shadow: ${({ theme }) => theme.shadows.md};
    }
  }

  &.secondary {
    background: ${({ theme }) => theme.colors.background.secondary};
    color: ${({ theme }) => theme.colors.text.primary};
    border: 1px solid ${({ theme }) => theme.colors.border.medium};

    &:hover {
      background: ${({ theme }) => theme.colors.background.tertiary};
    }
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    padding: ${({ theme }) => theme.spacing[2]}
      ${({ theme }) => theme.spacing[3]};
    font-size: ${({ theme }) => theme.fontSizes.sm};
  }
`;

const FiltersSection = styled(motion.div)`
  background: ${({ theme }) => theme.colors.background.secondary};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[6]};

  /* Compact filters on mobile */
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing[3]};
    margin-bottom: ${({ theme }) => theme.spacing[3]};

    /* Hide filters by default on mobile, show only when expanded */
    ${({ isExpanded }) =>
      !isExpanded &&
      `
      display: none;
    `}
  }
`;

const FiltersGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: ${({ theme }) => theme.spacing[4]};
  align-items: end;

  @media (max-width: ${({ theme }) => theme.breakpoints.lg}) {
    grid-template-columns: 1fr 1fr;
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    grid-template-columns: 1fr;
  }
`;

const FilterGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};

  /* Hide desktop search on mobile */
  &.desktop-search {
    @media (max-width: 768px) {
      display: none !important;
    }
  }
`;

const FilterLabel = styled.label`
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const SearchInput = styled.div`
  position: relative;
  display: flex;
  align-items: center;
`;

const MobileFilterToggle = styled.button`
  display: none;

  @media (max-width: 768px) {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: ${({ theme }) => theme.spacing[2]};
    width: 100%;
    padding: ${({ theme }) => theme.spacing[3]};
    margin-bottom: ${({ theme }) => theme.spacing[3]};
    background: ${({ theme }) => theme.colors.background.secondary};
    border: 1px solid ${({ theme }) => theme.colors.border.light};
    border-radius: ${({ theme }) => theme.borderRadius.md};
    color: ${({ theme }) => theme.colors.text.primary};
    font-size: ${({ theme }) => theme.fontSizes.base};
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: ${({ theme }) => theme.colors.background.tertiary};
    }

    svg {
      transition: transform 0.2s ease;
      transform: ${({ isExpanded }) =>
        isExpanded ? "rotate(180deg)" : "rotate(0deg)"};
    }
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  left: ${({ theme }) => theme.spacing[3]};
  color: ${({ theme }) => theme.colors.text.secondary};
  display: flex;
  align-items: center;
`;

const Input = styled.input`
  width: 100%;
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[3]}
    ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[10]};
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSizes.base};
  transition: all ${({ theme }) => theme.transitions.fast};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary[500]};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.primary[100]};
  }
`;

const Select = styled.select`
  padding: ${({ theme }) => theme.spacing[3]};
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSizes.base};
  background: ${({ theme }) => theme.colors.background.primary};
  transition: all ${({ theme }) => theme.transitions.fast};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary[500]};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.primary[100]};
  }
`;

const EventsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: ${({ theme }) => theme.spacing[4]};

  /* Better space utilization on larger screens */
  @media (min-width: ${({ theme }) => theme.breakpoints.md}) {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: ${({ theme }) => theme.spacing[5]};
  }

  @media (min-width: ${({ theme }) => theme.breakpoints.lg}) {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: ${({ theme }) => theme.spacing[6]};
  }

  @media (min-width: ${({ theme }) => theme.breakpoints.xl}) {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    grid-template-columns: 1fr;
    gap: ${({ theme }) => theme.spacing[3]};
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing[12]}
    ${({ theme }) => theme.spacing[4]};
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const LoadingState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing[8]} ${({ theme }) => theme.spacing[4]};
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const Pagination = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[4]};
  margin-top: ${({ theme }) => theme.spacing[8]};
`;

const PaginationButton = styled.button`
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[4]};
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background: ${({ theme, active }) =>
    active ? theme.colors.primary[600] : theme.colors.background.primary};
  color: ${({ theme, active }) =>
    active ? "white" : theme.colors.text.primary};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  transition: all ${({ theme }) => theme.transitions.fast};

  &:hover:not(:disabled) {
    background: ${({ theme, active }) =>
      active ? theme.colors.primary[700] : theme.colors.background.secondary};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const EventsPage = () => {
  const {
    events,
    isLoading,
    pagination,
    fetchEvents,
    createEvent,
    updateEvent,
    deleteEvent,
    exportEvents,
    setFilters,
    filters,
  } = useEventsStore();
  const { isAuthenticated, hasPermission } = useAuth();

  const [selectedEvent, setSelectedEvent] = useState(null);
  const [showEventModal, setShowEventModal] = useState(false);
  const [showEventForm, setShowEventForm] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [editingEvent, setEditingEvent] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [isExporting, setIsExporting] = useState(false);
  const [mobileFiltersExpanded, setMobileFiltersExpanded] = useState(false);

  useEffect(() => {
    fetchEvents({ page: 1, limit: 12 });
  }, [fetchEvents]);

  // Listen for mobile search events
  useEffect(() => {
    const handleMobileSearch = (event) => {
      console.log("EventsPage received mobile search:", event.detail.query);
      const query = event.detail.query;
      setSearchTerm(query);
      setFilters({ search: query });
      fetchEvents({ page: 1, limit: 12, search: query });

      // Update mobile header search indicator
      window.dispatchEvent(
        new CustomEvent("searchTermUpdate", {
          detail: { searchTerm: query },
        })
      );
    };

    console.log("EventsPage: Adding mobile search listener");
    window.addEventListener("mobileSearch", handleMobileSearch);

    return () => {
      console.log("EventsPage: Removing mobile search listener");
      window.removeEventListener("mobileSearch", handleMobileSearch);
    };
  }, [setFilters, fetchEvents]);

  const handleSearch = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    setFilters({ search: value });
    fetchEvents({ page: 1, limit: 12, search: value });

    // Update mobile header search indicator
    window.dispatchEvent(
      new CustomEvent("searchTermUpdate", {
        detail: { searchTerm: value },
      })
    );
  };

  const handleFilterChange = (filterName, value) => {
    setFilters({ [filterName]: value });
    fetchEvents({ page: 1, limit: 12, [filterName]: value });
  };

  const handlePageChange = (page) => {
    fetchEvents({ page, limit: 12 });
  };

  const handleEventClick = (event) => {
    setSelectedEvent(event);
    setShowEventModal(true);
  };

  const handleCreateEvent = () => {
    setEditingEvent(null);
    setShowEventForm(true);
  };

  const handleEditEvent = (event) => {
    setEditingEvent(event);
    setShowEventForm(true);
    setShowEventModal(false);
  };

  const handleDeleteEvent = async (event) => {
    if (window.confirm("Are you sure you want to delete this event?")) {
      try {
        await deleteEvent(event._id);

        // Note: fetchEvents is not needed here as the store automatically updates
        // the events list when deleteEvent is called

        setShowEventModal(false);
      } catch (error) {
        console.error("Failed to delete event:", error);
        alert("Failed to delete event. Please try again.");
      }
    }
  };

  const handleFormSubmit = async (formData) => {
    try {
      if (editingEvent) {
        await updateEvent(editingEvent._id, formData);
      } else {
        await createEvent(formData);
      }

      // Refresh the events list to show the new/updated event
      await fetchEvents();

      setShowEventForm(false);
      setEditingEvent(null);
    } catch (error) {
      console.error("Failed to save event:", error);
      alert("Failed to save event. Please try again.");
    }
  };

  const handleExport = async (formatParam) => {
    try {
      setIsExporting(true);

      // Extract format string from parameter
      const format =
        typeof formatParam === "object" && formatParam.format
          ? formatParam.format
          : formatParam;

      const exportParams = { format };

      const response = await exportEvents(exportParams);

      console.log("Export response:", {
        hasData: !!response.data,
        dataType: typeof response.data,
        dataSize: response.data?.byteLength || response.data?.length || 0,
        isArrayBuffer: response.data instanceof ArrayBuffer,
      });

      // Check PDF header for debugging
      if (format === "pdf" && response.data instanceof ArrayBuffer) {
        const uint8Array = new Uint8Array(response.data);
        const firstBytes = Array.from(uint8Array.slice(0, 10))
          .map((b) => b.toString(16).padStart(2, "0"))
          .join(" ");
        console.log("First 10 bytes of PDF:", firstBytes);

        const pdfHeader = Array.from(uint8Array.slice(0, 4))
          .map((b) => String.fromCharCode(b))
          .join("");
        console.log("PDF header:", pdfHeader);
        console.log("Is valid PDF header:", pdfHeader === "%PDF");

        // Check if it looks like an error response instead
        const textStart = Array.from(uint8Array.slice(0, 50))
          .map((b) => String.fromCharCode(b))
          .join("");
        console.log("Text content start:", textStart);
      }

      if (response && response.data) {
        // Generate filename
        const currentDate = new Date().toISOString().split("T")[0];
        let filename = `grace-bible-fellowship-events-${currentDate}`;
        if (filters.category) {
          filename += `-${filters.category}`;
        }
        if (filters.startDate && filters.endDate) {
          filename += `-${filters.startDate}-to-${filters.endDate}`;
        }

        let blob, fileExtension;

        switch (format) {
          case "pdf":
            blob = new Blob([response.data], { type: "application/pdf" });
            fileExtension = "pdf";
            break;
          case "word":
          case "docx":
            blob = new Blob([response.data], {
              type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            });
            fileExtension = "docx";
            break;
          case "csv":
          default:
            blob = new Blob([response.data], {
              type: "text/csv;charset=utf-8;",
            });
            fileExtension = "csv";
            break;
        }

        console.log("Blob created:", { size: blob.size, type: blob.type });

        // Create download link
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `${filename}.${fileExtension}`;

        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up
        URL.revokeObjectURL(url);

        setShowExportModal(false);
        alert(
          `Events exported successfully! Downloaded as ${filename}.${fileExtension}`
        );
      } else {
        throw new Error("No data received from export");
      }
    } catch (error) {
      console.error("Failed to export events:", error);
      alert(`Export failed: ${error.message}`);
    } finally {
      setIsExporting(false);
    }
  };

  const handleExportClick = () => {
    setShowExportModal(true);
  };

  const canCreateEvents = isAuthenticated && hasPermission("canCreateEvents");
  const canExportData = isAuthenticated && hasPermission("canExportData");

  return (
    <EventsContainer>
      <PageHeader>
        <PageTitle>All Events</PageTitle>
        <HeaderActions>
          {canExportData && (
            <ActionButton
              className="secondary export-button"
              onClick={handleExportClick}
            >
              <Download size={20} />
              Export
            </ActionButton>
          )}
          {canCreateEvents && (
            <ActionButton className="primary" onClick={handleCreateEvent}>
              <Plus size={20} />
              Create Event
            </ActionButton>
          )}
        </HeaderActions>
      </PageHeader>

      <MobileFilterToggle
        isExpanded={mobileFiltersExpanded}
        onClick={() => setMobileFiltersExpanded(!mobileFiltersExpanded)}
      >
        <span>Filters</span>
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M7 10l5 5 5-5z" />
        </svg>
      </MobileFilterToggle>

      <FiltersSection
        isExpanded={mobileFiltersExpanded}
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <FiltersGrid>
          <FilterGroup className="desktop-search">
            <FilterLabel>Search Events</FilterLabel>
            <SearchInput>
              <SearchIcon>
                <Search size={18} />
              </SearchIcon>
              <Input
                type="text"
                placeholder="Search by title, description, or location..."
                value={searchTerm}
                onChange={handleSearch}
              />
            </SearchInput>
          </FilterGroup>

          <FilterGroup>
            <FilterLabel>Category</FilterLabel>
            <Select
              value={filters.category}
              onChange={(e) => handleFilterChange("category", e.target.value)}
            >
              <option value="">All Categories</option>
              {EVENT_CATEGORIES.map((category) => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </Select>
          </FilterGroup>

          <FilterGroup>
            <FilterLabel>Status</FilterLabel>
            <Select
              value={filters.status}
              onChange={(e) => handleFilterChange("status", e.target.value)}
            >
              <option value="">All Statuses</option>
              {EVENT_STATUSES.map((status) => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </Select>
          </FilterGroup>

          <FilterGroup>
            <FilterLabel>Date Range</FilterLabel>
            <Select
              onChange={(e) => {
                const value = e.target.value;
                const today = new Date();
                let startDate = "";
                let endDate = "";

                if (value === "today") {
                  startDate = today.toISOString().split("T")[0];
                  endDate = startDate;
                } else if (value === "week") {
                  startDate = today.toISOString().split("T")[0];
                  const nextWeek = new Date(today);
                  nextWeek.setDate(today.getDate() + 7);
                  endDate = nextWeek.toISOString().split("T")[0];
                } else if (value === "month") {
                  startDate = today.toISOString().split("T")[0];
                  const nextMonth = new Date(today);
                  nextMonth.setMonth(today.getMonth() + 1);
                  endDate = nextMonth.toISOString().split("T")[0];
                }

                setFilters({ startDate, endDate });
                fetchEvents({ page: 1, limit: 12, startDate, endDate });
              }}
            >
              <option value="">All Dates</option>
              <option value="today">Today</option>
              <option value="week">Next 7 Days</option>
              <option value="month">Next 30 Days</option>
            </Select>
          </FilterGroup>
        </FiltersGrid>
      </FiltersSection>

      {isLoading ? (
        <LoadingState>Loading events...</LoadingState>
      ) : events.length > 0 ? (
        <>
          <EventsGrid>
            {events.map((event) => (
              <EventCard
                key={event._id}
                event={event}
                onClick={handleEventClick}
                onEdit={handleEditEvent}
                onDelete={handleDeleteEvent}
              />
            ))}
          </EventsGrid>

          {pagination.pages > 1 && (
            <Pagination>
              <PaginationButton
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1}
              >
                Previous
              </PaginationButton>

              {Array.from({ length: pagination.pages }, (_, i) => i + 1).map(
                (page) => (
                  <PaginationButton
                    key={page}
                    active={page === pagination.page}
                    onClick={() => handlePageChange(page)}
                  >
                    {page}
                  </PaginationButton>
                )
              )}

              <PaginationButton
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page === pagination.pages}
              >
                Next
              </PaginationButton>
            </Pagination>
          )}
        </>
      ) : (
        <EmptyState>
          <h3>No events found</h3>
          <p>Try adjusting your search criteria or create a new event.</p>
        </EmptyState>
      )}

      <EventModal
        event={selectedEvent}
        isOpen={showEventModal}
        onClose={() => setShowEventModal(false)}
        onEdit={handleEditEvent}
        onDelete={handleDeleteEvent}
      />

      <EventForm
        event={editingEvent}
        selectedDate={null}
        isOpen={showEventForm}
        onClose={() => {
          setShowEventForm(false);
          setEditingEvent(null);
        }}
        onSubmit={handleFormSubmit}
        isLoading={isLoading}
      />

      <ExportModal
        isOpen={showExportModal}
        onClose={() => setShowExportModal(false)}
        onExport={handleExport}
        isLoading={isExporting}
      />
    </EventsContainer>
  );
};

export default EventsPage;
