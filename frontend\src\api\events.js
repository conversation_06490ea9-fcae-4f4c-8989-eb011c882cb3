import api from "./config";

export const eventsAPI = {
  // Get all events
  getEvents: async (params = {}) => {
    const response = await api.get("/events", { params });
    return response.data;
  },

  // Get single event
  getEvent: async (id) => {
    const response = await api.get(`/events/${id}`);
    return response.data;
  },

  // Create new event
  createEvent: async (eventData) => {
    const response = await api.post("/events", eventData);
    return response.data;
  },

  // Update event
  updateEvent: async (id, eventData) => {
    const response = await api.put(`/events/${id}`, eventData);
    return response.data;
  },

  // Delete event
  deleteEvent: async (id) => {
    const response = await api.delete(`/events/${id}`);
    return response.data;
  },

  // Export events
  exportEvents: async (params = {}) => {
    const { format = "csv" } = params;

    if (format === "csv") {
      // Use axios for CSV (text)
      const response = await api.get("/events/export/data", {
        params,
        responseType: "text",
      });
      return response;
    } else {
      // Use fetch for binary data (PDF/Word)
      // Extract format string from params
      let format = "json";
      if (params.format) {
        if (typeof params.format === "string") {
          format = params.format;
        } else if (typeof params.format === "object" && params.format.format) {
          format = params.format.format;
        } else {
          format = String(params.format);
        }
      }

      const url = `${api.defaults.baseURL}/events/export/data?format=${format}`;

      // Get the token from localStorage or wherever it's stored
      const token = localStorage.getItem("token");

      const headers = {
        "Content-Type": "application/json",
      };

      if (token) {
        headers["Authorization"] = `Bearer ${token}`;
      }

      const response = await fetch(url, {
        method: "GET",
        headers,
      });

      if (!response.ok) {
        throw new Error(`Export failed: ${response.statusText}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      return { data: arrayBuffer };
    }
  },

  // Get calendar events
  getCalendarEvents: async (year, month) => {
    const response = await api.get(`/events/calendar/${year}/${month}`);
    return response.data;
  },

  // Search events
  searchEvents: async (query, params = {}) => {
    const response = await api.get("/events", {
      params: { ...params, search: query },
    });
    return response.data;
  },

  // Upload files
  uploadFiles: async (files) => {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append("files", file);
    });

    const response = await api.post("/events/upload", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  },

  // Delete file
  deleteFile: async (filename) => {
    const response = await api.delete(`/events/upload/${filename}`);
    return response.data;
  },
};
