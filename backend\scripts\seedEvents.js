const mongoose = require("mongoose");
const { Event, User } = require("../models");
require("dotenv").config();

const seedEvents = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(
      process.env.MONGODB_URI || "mongodb://localhost:27017/church-events"
    );
    console.log("Connected to MongoDB");

    // Check if events already exist
    const existingEvents = await Event.countDocuments();
    
    if (existingEvents > 0) {
      console.log(`${existingEvents} events already exist in the database.`);
      process.exit(0);
    }

    // Get admin and editor users for event creation
    const admin = await User.findOne({ role: "admin" });
    const editor = await User.findOne({ role: "editor" });

    if (!admin || !editor) {
      console.log("❌ Admin or Editor user not found. Please run seed:admin first.");
      process.exit(1);
    }

    // Sample events data
    const sampleEvents = [
      {
        title: "Sunday Morning Worship",
        description: "Join us for our weekly Sunday morning worship service. Experience uplifting music, inspiring messages, and fellowship with our church community.",
        startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Next week
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        startTime: "10:00",
        endTime: "11:30",
        location: {
          name: "Main Sanctuary",
          address: "123 Church Street, City, State 12345",
          room: "Sanctuary"
        },
        category: "worship",
        priority: "high",
        status: "published",
        isPublic: true,
        organizer: admin._id,
        createdBy: admin._id,
        attendees: {
          expected: 150,
          registered: 0
        }
      },
      {
        title: "Wednesday Prayer Meeting",
        description: "Come together for a time of prayer, reflection, and spiritual growth. All are welcome to join us in seeking God's guidance and blessing.",
        startDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // In 3 days
        endDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
        startTime: "19:00",
        endTime: "20:00",
        location: {
          name: "Fellowship Hall",
          address: "123 Church Street, City, State 12345",
          room: "Fellowship Hall"
        },
        category: "prayer",
        priority: "medium",
        status: "published",
        isPublic: true,
        organizer: editor._id,
        createdBy: editor._id,
        attendees: {
          expected: 50,
          registered: 0
        }
      },
      {
        title: "Youth Bible Study",
        description: "An engaging Bible study session designed for teenagers and young adults. Explore faith, ask questions, and build lasting friendships.",
        startDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // In 5 days
        endDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
        startTime: "18:30",
        endTime: "20:00",
        location: {
          name: "Youth Room",
          address: "123 Church Street, City, State 12345",
          room: "Youth Center"
        },
        category: "youth",
        priority: "medium",
        status: "published",
        isPublic: true,
        organizer: editor._id,
        createdBy: editor._id,
        attendees: {
          expected: 25,
          registered: 0
        }
      },
      {
        title: "Community Outreach Event",
        description: "Join us as we serve our local community through food distribution, clothing drive, and fellowship. Volunteers needed!",
        startDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // In 2 weeks
        endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
        startTime: "09:00",
        endTime: "15:00",
        location: {
          name: "Community Center",
          address: "456 Community Ave, City, State 12345",
          room: "Main Hall"
        },
        category: "outreach",
        priority: "high",
        status: "published",
        isPublic: true,
        organizer: admin._id,
        createdBy: admin._id,
        attendees: {
          expected: 100,
          registered: 0
        }
      },
      {
        title: "Children's Sunday School",
        description: "Fun and interactive Sunday school for children ages 5-12. Bible stories, games, crafts, and snacks included!",
        startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Next week
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        startTime: "09:00",
        endTime: "09:45",
        location: {
          name: "Children's Wing",
          address: "123 Church Street, City, State 12345",
          room: "Room 101"
        },
        category: "children",
        priority: "medium",
        status: "published",
        isPublic: true,
        organizer: editor._id,
        createdBy: editor._id,
        attendees: {
          expected: 30,
          registered: 0
        }
      },
      {
        title: "Annual Church Conference",
        description: "Our annual church conference featuring guest speakers, workshops, and fellowship. Registration required.",
        startDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // In 1 month
        endDate: new Date(Date.now() + 32 * 24 * 60 * 60 * 1000), // 3-day event
        startTime: "09:00",
        endTime: "17:00",
        location: {
          name: "Main Sanctuary",
          address: "123 Church Street, City, State 12345",
          room: "Sanctuary & Fellowship Hall"
        },
        category: "conference",
        priority: "high",
        status: "published",
        isPublic: true,
        organizer: admin._id,
        createdBy: admin._id,
        attendees: {
          expected: 200,
          registered: 0
        },
        requiresRegistration: true,
        maxAttendees: 250
      },
      {
        title: "Draft Event - Planning Meeting",
        description: "Internal planning meeting for upcoming events. This is a draft event to test the draft status.",
        startDate: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000), // In 3 weeks
        endDate: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000),
        startTime: "14:00",
        endTime: "16:00",
        location: {
          name: "Conference Room",
          address: "123 Church Street, City, State 12345",
          room: "Conference Room A"
        },
        category: "other",
        priority: "low",
        status: "draft",
        isPublic: false,
        organizer: editor._id,
        createdBy: editor._id,
        attendees: {
          expected: 10,
          registered: 0
        }
      }
    ];

    // Create events
    const createdEvents = await Event.insertMany(sampleEvents);

    console.log("✅ Sample events created successfully!");
    console.log(`📅 Created ${createdEvents.length} events:`);
    
    createdEvents.forEach((event, index) => {
      console.log(`   ${index + 1}. ${event.title} (${event.status})`);
    });

    console.log("\n🎉 Event seeding completed!");
    console.log("\n📋 Event Summary:");
    console.log("┌─────────────────────────────────────────────────┐");
    console.log("│ PUBLISHED EVENTS (Visible to all)              │");
    console.log("│ • Sunday Morning Worship                        │");
    console.log("│ • Wednesday Prayer Meeting                      │");
    console.log("│ • Youth Bible Study                             │");
    console.log("│ • Community Outreach Event                     │");
    console.log("│ • Children's Sunday School                      │");
    console.log("│ • Annual Church Conference                      │");
    console.log("├─────────────────────────────────────────────────┤");
    console.log("│ DRAFT EVENTS (Only visible to editors/admins)  │");
    console.log("│ • Draft Event - Planning Meeting               │");
    console.log("└─────────────────────────────────────────────────┘");

    process.exit(0);

  } catch (error) {
    console.error("Error seeding events:", error);
    process.exit(1);
  }
};

// Run the seed function
seedEvents();
