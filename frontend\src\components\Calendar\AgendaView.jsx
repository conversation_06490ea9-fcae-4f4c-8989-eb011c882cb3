import React from "react";
import styled from "styled-components";
import { motion } from "framer-motion";
import { Calendar, Clock, MapPin, Users } from "lucide-react";
import {
  formatDate,
  formatTime,
  getRelativeDate,
  isToday,
  isPast,
} from "../../utils/dateUtils";
import { EVENT_CATEGORIES } from "../../utils/constants";

const AgendaContainer = styled.div`
  padding: ${({ theme }) => theme.spacing[4]};
  max-height: 600px;
  overflow-y: auto;

  /* Desktop web view - spacious agenda container */
  @media (min-width: 769px) {
    padding: ${({ theme }) => theme.spacing[6]};
    max-height: 700px;
  }

  /* Mobile native feel - compact agenda with smooth scrolling */
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing[3]};
    max-height: 500px;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
  }
`;

const EventsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[4]};
`;

const DateGroup = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const DateHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[3]} 0;
  border-bottom: 2px solid
    ${({ theme, isToday }) =>
      isToday ? theme.colors.primary[500] : theme.colors.border.light};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const DateTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme, isToday }) =>
    isToday ? theme.colors.primary[700] : theme.colors.text.primary};
  margin: 0;
`;

const RelativeDate = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme, isToday }) =>
    isToday ? theme.colors.primary[600] : theme.colors.text.secondary};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
`;

const EventCard = styled(motion.div)`
  background: ${({ theme }) => theme.colors.background.primary};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  border-left: 4px solid
    ${({ theme, category }) => {
      const categoryData = EVENT_CATEGORIES.find(
        (cat) => cat.value === category
      );
      return categoryData ? theme.colors.primary[500] : theme.colors.gray[400];
    }};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[4]};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};
  opacity: ${({ isPast }) => (isPast ? 0.7 : 1)};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
    border-color: ${({ theme }) => theme.colors.primary[300]};
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    padding: ${({ theme }) => theme.spacing[3]};
  }
`;

const EventHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${({ theme }) => theme.spacing[3]};
  gap: ${({ theme }) => theme.spacing[3]};
`;

const EventTitle = styled.h4`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
  line-height: ${({ theme }) => theme.lineHeights.tight};
`;

const EventStatus = styled.span`
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  background: ${({ theme, status }) => {
    switch (status) {
      case "published":
        return theme.colors.success[100];
      case "draft":
        return theme.colors.gray[100];
      case "cancelled":
        return theme.colors.error[100];
      case "completed":
        return theme.colors.info[100];
      default:
        return theme.colors.gray[100];
    }
  }};
  color: ${({ theme, status }) => {
    switch (status) {
      case "published":
        return theme.colors.success[800];
      case "draft":
        return theme.colors.gray[800];
      case "cancelled":
        return theme.colors.error[800];
      case "completed":
        return theme.colors.info[800];
      default:
        return theme.colors.gray[800];
    }
  }};
`;

const EventDetails = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing[3]};
  margin-bottom: ${({ theme }) => theme.spacing[3]};

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    grid-template-columns: 1fr;
    gap: ${({ theme }) => theme.spacing[2]};
  }
`;

const DetailItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const DetailIcon = styled.div`
  color: ${({ theme }) => theme.colors.primary[500]};
  display: flex;
  align-items: center;
`;

const EventDescription = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  line-height: ${({ theme }) => theme.lineHeights.relaxed};
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const CategoryBadge = styled.span`
  display: inline-flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  background: ${({ theme }) => theme.colors.primary[100]};
  color: ${({ theme }) => theme.colors.primary[800]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  margin-top: ${({ theme }) => theme.spacing[2]};
`;

const NoEventsMessage = styled.div`
  text-align: center;
  color: ${({ theme }) => theme.colors.text.secondary};
  font-style: italic;
  padding: ${({ theme }) => theme.spacing[8]};
  background: ${({ theme }) => theme.colors.background.secondary};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
`;

const AgendaView = ({ events, onEventClick }) => {
  // Sort events by date and time
  const sortedEvents = [...events].sort((a, b) => {
    const dateA = new Date(`${a.startDate}T${a.startTime}`);
    const dateB = new Date(`${b.startDate}T${b.startTime}`);
    return dateA - dateB;
  });

  // Group events by date
  const eventsByDate = sortedEvents.reduce((acc, event) => {
    const dateKey = formatDate(event.startDate, "yyyy-MM-dd");
    if (!acc[dateKey]) {
      acc[dateKey] = [];
    }
    acc[dateKey].push(event);
    return acc;
  }, {});

  const handleEventClick = (event) => {
    if (onEventClick) {
      onEventClick(event);
    }
  };

  const getCategoryLabel = (categoryValue) => {
    const category = EVENT_CATEGORIES.find(
      (cat) => cat.value === categoryValue
    );
    return category ? category.label : categoryValue;
  };

  if (sortedEvents.length === 0) {
    return (
      <AgendaContainer>
        <NoEventsMessage>No upcoming events scheduled</NoEventsMessage>
      </AgendaContainer>
    );
  }

  return (
    <AgendaContainer>
      <EventsList>
        {Object.entries(eventsByDate).map(([dateKey, dateEvents]) => {
          const eventDate = new Date(dateKey);
          const isDayToday = isToday(eventDate);
          const relativeDate = getRelativeDate(eventDate);

          return (
            <DateGroup key={dateKey}>
              <DateHeader isToday={isDayToday}>
                <Calendar size={20} />
                <DateTitle isToday={isDayToday}>
                  {formatDate(eventDate, "EEEE, MMMM dd, yyyy")}
                </DateTitle>
                <RelativeDate isToday={isDayToday}>{relativeDate}</RelativeDate>
              </DateHeader>

              {dateEvents.map((event, index) => {
                const eventIsPast = isPast(
                  new Date(`${event.startDate}T${event.startTime}`)
                );

                return (
                  <EventCard
                    key={event._id}
                    category={event.category}
                    isPast={eventIsPast}
                    onClick={() => handleEventClick(event)}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <EventHeader>
                      <EventTitle>{event.title}</EventTitle>
                      <EventStatus status={event.status}>
                        {event.status}
                      </EventStatus>
                    </EventHeader>

                    <EventDetails>
                      <DetailItem>
                        <DetailIcon>
                          <Clock size={16} />
                        </DetailIcon>
                        {formatTime(event.startTime)} -{" "}
                        {formatTime(event.endTime)}
                      </DetailItem>

                      {event.location?.name && (
                        <DetailItem>
                          <DetailIcon>
                            <MapPin size={16} />
                          </DetailIcon>
                          {event.location.name}
                        </DetailItem>
                      )}

                      {event.attendees?.expected > 0 && (
                        <DetailItem>
                          <DetailIcon>
                            <Users size={16} />
                          </DetailIcon>
                          {event.attendees.expected} expected
                        </DetailItem>
                      )}
                    </EventDetails>

                    {event.description && (
                      <EventDescription>{event.description}</EventDescription>
                    )}

                    <CategoryBadge>
                      {getCategoryLabel(event.category)}
                    </CategoryBadge>
                  </EventCard>
                );
              })}
            </DateGroup>
          );
        })}
      </EventsList>
    </AgendaContainer>
  );
};

export default AgendaView;
