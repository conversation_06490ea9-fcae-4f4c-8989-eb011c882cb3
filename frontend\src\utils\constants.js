// Event categories
export const EVENT_CATEGORIES = [
  { value: "worship", label: "Worship Service" },
  { value: "prayer", label: "Prayer Meeting" },
  { value: "bible-study", label: "Bible Study" },
  { value: "youth", label: "Youth Ministry" },
  { value: "children", label: "Children Ministry" },
  { value: "fellowship", label: "Fellowship" },
  { value: "outreach", label: "Outreach" },
  { value: "conference", label: "Conference" },
  { value: "wedding", label: "Wedding" },
  { value: "funeral", label: "Funeral" },
  { value: "baptism", label: "Baptism" },
  { value: "communion", label: "Communion" },
  { value: "special-service", label: "Special Service" },
  { value: "community", label: "Community Event" },
  { value: "other", label: "Other" },
];

// Event priorities
export const EVENT_PRIORITIES = [
  { value: "low", label: "Low", color: "#10b981" },
  { value: "medium", label: "Medium", color: "#f59e0b" },
  { value: "high", label: "High", color: "#ef4444" },
  { value: "urgent", label: "Urgent", color: "#dc2626" },
];

// Event statuses
export const EVENT_STATUSES = [
  { value: "draft", label: "Draft", color: "#6b7280" },
  { value: "published", label: "Published", color: "#10b981" },
  { value: "cancelled", label: "Cancelled", color: "#ef4444" },
  { value: "completed", label: "Completed", color: "#3b82f6" },
];

// User roles
export const USER_ROLES = [
  { value: "admin", label: "Administrator" },
  { value: "editor", label: "Editor" },
];

// Calendar view types
export const CALENDAR_VIEWS = [
  { value: "month", label: "Month" },
  { value: "week", label: "Week" },
  { value: "day", label: "Day" },
  { value: "agenda", label: "Agenda" },
];

// Recurrence patterns
export const RECURRENCE_PATTERNS = [
  { value: "daily", label: "Daily" },
  { value: "weekly", label: "Weekly" },
  { value: "monthly", label: "Monthly" },
  { value: "yearly", label: "Yearly" },
];

// Days of week
export const DAYS_OF_WEEK = [
  { value: 0, label: "Sunday", short: "Sun" },
  { value: 1, label: "Monday", short: "Mon" },
  { value: 2, label: "Tuesday", short: "Tue" },
  { value: 3, label: "Wednesday", short: "Wed" },
  { value: 4, label: "Thursday", short: "Thu" },
  { value: 5, label: "Friday", short: "Fri" },
  { value: 6, label: "Saturday", short: "Sat" },
];

// Export formats
export const EXPORT_FORMATS = [
  { value: "json", label: "JSON" },
  { value: "csv", label: "CSV" },
];

// Pagination limits
export const PAGINATION_LIMITS = [
  { value: 10, label: "10 per page" },
  { value: 25, label: "25 per page" },
  { value: 50, label: "50 per page" },
  { value: 100, label: "100 per page" },
];

// Sort options for events
export const EVENT_SORT_OPTIONS = [
  { value: "startDate", label: "Start Date (Ascending)" },
  { value: "-startDate", label: "Start Date (Descending)" },
  { value: "title", label: "Title (A-Z)" },
  { value: "-title", label: "Title (Z-A)" },
  { value: "createdAt", label: "Created Date (Ascending)" },
  { value: "-createdAt", label: "Created Date (Descending)" },
];

// Default form values
export const DEFAULT_EVENT_FORM = {
  title: "",
  description: "",
  startDate: "",
  endDate: "",
  startTime: "09:00",
  endTime: "10:00",
  location: {
    name: "",
    address: "",
    room: "",
  },
  category: "worship",
  priority: "medium",
  status: "draft",
  pointOfContact: {
    name: "",
    email: "",
    phone: "",
  },
  isRecurring: false,
  recurrence: {
    pattern: "weekly",
    interval: 1,
    daysOfWeek: [],
    endDate: "",
  },
  attendees: {
    expected: 0,
    registered: 0,
  },
  resources: {
    equipment: [],
    materials: [],
    volunteers: [],
  },
  images: [],
  attachments: [],
  tags: [],
  isPublic: true,
  requiresRegistration: false,
  maxAttendees: null,
  cost: {
    amount: 0,
    currency: "USD",
    isFree: true,
  },
  contactInfo: {
    name: "",
    email: "",
    phone: "",
  },
  notes: "",
};

// API endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: "/auth/login",
    LOGOUT: "/auth/logout",
    PROFILE: "/auth/me",
    UPDATE_PROFILE: "/auth/profile",
    CHANGE_PASSWORD: "/auth/change-password",
    VERIFY: "/auth/verify",
  },
  EVENTS: {
    BASE: "/events",
    EXPORT: "/events/export/data",
    CALENDAR: "/events/calendar",
  },
  USERS: {
    BASE: "/users",
  },
};

// Local storage keys
export const STORAGE_KEYS = {
  TOKEN: "token",
  USER: "user",
  THEME: "theme",
  CALENDAR_VIEW: "calendar_view",
  FILTERS: "event_filters",
};

// Error messages
export const ERROR_MESSAGES = {
  NETWORK: "Network error. Please check your connection.",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  FORBIDDEN: "Access denied. Insufficient permissions.",
  NOT_FOUND: "The requested resource was not found.",
  SERVER_ERROR: "Server error. Please try again later.",
  VALIDATION: "Please check your input and try again.",
  UNKNOWN: "An unexpected error occurred.",
};

// Success messages
export const SUCCESS_MESSAGES = {
  LOGIN: "Login successful!",
  LOGOUT: "Logout successful!",
  EVENT_CREATED: "Event created successfully!",
  EVENT_UPDATED: "Event updated successfully!",
  EVENT_DELETED: "Event deleted successfully!",
  PROFILE_UPDATED: "Profile updated successfully!",
  PASSWORD_CHANGED: "Password changed successfully!",
  USER_CREATED: "User created successfully!",
  USER_UPDATED: "User updated successfully!",
  USER_DELETED: "User deleted successfully!",
  EXPORT_SUCCESS: "Data exported successfully!",
};

// Validation rules
export const VALIDATION_RULES = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^[\+]?[1-9][\d]{0,15}$/,
  TIME: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,
  USERNAME: /^[a-zA-Z0-9_]{3,30}$/,
  PASSWORD_MIN_LENGTH: 6,
  TITLE_MAX_LENGTH: 100,
  DESCRIPTION_MAX_LENGTH: 2000,
  NAME_MAX_LENGTH: 50,
};
