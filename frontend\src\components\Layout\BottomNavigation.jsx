import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import styled from "styled-components";
import { motion } from "framer-motion";
import { Home, Calendar, Users, User, Settings, Plus } from "lucide-react";
import { useAuth } from "../../hooks/useAuth";
import { useStandalone } from "../../hooks/useTouch";

const BottomNavContainer = styled(motion.nav)`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: ${({ theme }) => theme.colors.background.primary};
  border-top: 1px solid ${({ theme }) => theme.colors.border.light};
  padding: ${({ theme }) => theme.spacing[2]} 0;
  padding-bottom: ${({ isStandalone, theme }) =>
    isStandalone ? "env(safe-area-inset-bottom)" : theme.spacing[2]};
  z-index: ${({ theme }) => theme.zIndex[50]};
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);

  @media (min-width: 769px) {
    display: none;
  }
`;

const NavGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(${({ itemCount }) => itemCount}, 1fr);
  max-width: 100%;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing[4]};
`;

const NavItem = styled(motion.button)`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[1]};
  background: none;
  border: none;
  cursor: pointer;
  position: relative;
  min-height: 60px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;

  &:focus {
    outline: none;
  }
`;

const NavIcon = styled(motion.div)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin-bottom: ${({ theme }) => theme.spacing[1]};
  color: ${({ theme, isActive }) =>
    isActive ? theme.colors.primary[600] : theme.colors.text.tertiary};
  transition: color ${({ theme }) => theme.transitions.fast};
`;

const NavLabel = styled(motion.span)`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme, isActive }) =>
    isActive ? theme.fontWeights.semibold : theme.fontWeights.normal};
  color: ${({ theme, isActive }) =>
    isActive ? theme.colors.primary[600] : theme.colors.text.tertiary};
  text-align: center;
  line-height: 1.2;
  transition: color ${({ theme }) => theme.transitions.fast};
`;

const ActiveIndicator = styled(motion.div)`
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 3px;
  background: ${({ theme }) => theme.colors.primary[600]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
`;

const FAB = styled(motion.button)`
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 56px;
  height: 56px;
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors.primary[500]},
    ${({ theme }) => theme.colors.primary[600]}
  );
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  box-shadow: ${({ theme }) => theme.shadows.lg};
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;

  &:focus {
    outline: none;
  }

  &:active {
    transform: translateX(-50%) scale(0.95);
  }
`;

const BottomNavigation = ({ onCreateEvent }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuth();
  const isStandalone = useStandalone();

  const publicNavItems = [
    {
      path: "/",
      icon: Home,
      label: "Home",
      exact: true,
    },
    {
      path: "/calendar",
      icon: Calendar,
      label: "Calendar",
    },
    {
      path: "/events",
      icon: Users,
      label: "Events",
    },
  ];

  const authenticatedNavItems = [
    ...publicNavItems,
    {
      path: "/profile",
      icon: User,
      label: "Profile",
    },
  ];

  const adminNavItems = [
    ...authenticatedNavItems,
    {
      path: "/admin",
      icon: Settings,
      label: "Admin",
    },
  ];

  const getNavItems = () => {
    if (user?.role === "admin") return adminNavItems;
    if (isAuthenticated) return authenticatedNavItems;
    return publicNavItems;
  };

  const navItems = getNavItems();
  const showFAB =
    isAuthenticated && (user?.role === "admin" || user?.role === "editor");

  const isActive = (item) => {
    if (item.exact) {
      return location.pathname === item.path;
    }
    return location.pathname.startsWith(item.path);
  };

  const handleNavigation = (path) => {
    navigate(path);
  };

  const handleCreateEvent = () => {
    if (onCreateEvent) {
      onCreateEvent();
    }
  };

  return (
    <BottomNavContainer
      isStandalone={isStandalone}
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      <NavGrid itemCount={navItems.length}>
        {navItems.map((item, index) => {
          const Icon = item.icon;
          const active = isActive(item);

          return (
            <NavItem
              key={item.path}
              onClick={() => handleNavigation(item.path)}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              {active && (
                <ActiveIndicator
                  layoutId="activeIndicator"
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                />
              )}

              <NavIcon
                isActive={active}
                animate={{ scale: active ? 1.1 : 1 }}
                transition={{ type: "spring", stiffness: 300, damping: 30 }}
              >
                <Icon size={20} />
              </NavIcon>

              <NavLabel isActive={active}>{item.label}</NavLabel>
            </NavItem>
          );
        })}
      </NavGrid>

      {showFAB && (
        <FAB
          onClick={handleCreateEvent}
          whileTap={{ scale: 0.9 }}
          whileHover={{ scale: 1.05 }}
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 20,
            delay: 0.3,
          }}
        >
          <Plus size={24} />
        </FAB>
      )}
    </BottomNavContainer>
  );
};

export default BottomNavigation;
