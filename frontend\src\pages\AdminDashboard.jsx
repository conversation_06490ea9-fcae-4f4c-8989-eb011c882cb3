import React, { useState, useEffect } from 'react';
import { Routes, Route, Link, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { 
  Users, 
  Calendar, 
  BarChart3, 
  Settings, 
  Plus,
  Edit,
  Trash2,
  Download
} from 'lucide-react';
import { useEventsStore } from '../store';
import { usersAPI } from '../api';

const AdminContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing[6]} 0;
`;

const AdminHeader = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`;

const AdminTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0 0 ${({ theme }) => theme.spacing[2]} 0;
`;

const AdminSubtitle = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: ${({ theme }) => theme.fontSizes.lg};
  margin: 0;
`;

const AdminNav = styled.nav`
  display: flex;
  gap: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  overflow-x: auto;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    gap: ${({ theme }) => theme.spacing[2]};
  }
`;

const NavLink = styled(Link)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[4]};
  color: ${({ theme, active }) => 
    active ? theme.colors.primary[600] : theme.colors.text.secondary};
  text-decoration: none;
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  border-bottom: 2px solid ${({ theme, active }) => 
    active ? theme.colors.primary[600] : 'transparent'};
  transition: all ${({ theme }) => theme.transitions.fast};
  white-space: nowrap;
  
  &:hover {
    color: ${({ theme }) => theme.colors.primary[600]};
    text-decoration: none;
  }
  
  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
    font-size: ${({ theme }) => theme.fontSizes.sm};
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing[6]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`;

const StatCard = styled(motion.div)`
  background: ${({ theme }) => theme.colors.background.primary};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  box-shadow: ${({ theme }) => theme.shadows.md};
  padding: ${({ theme }) => theme.spacing[6]};
  border-left: 4px solid ${({ theme, color }) => color || theme.colors.primary[500]};
`;

const StatIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  background: ${({ theme, color }) => color || theme.colors.primary[100]};
  color: ${({ theme, iconColor }) => iconColor || theme.colors.primary[600]};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const StatValue = styled.div`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing[1]};
`;

const StatLabel = styled.div`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
`;

const ContentArea = styled.div`
  background: ${({ theme }) => theme.colors.background.primary};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  box-shadow: ${({ theme }) => theme.shadows.md};
  padding: ${({ theme }) => theme.spacing[6]};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing[12]};
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const AdminDashboard = () => {
  const location = useLocation();
  const { events, fetchEvents } = useEventsStore();
  const [stats, setStats] = useState({
    totalEvents: 0,
    publishedEvents: 0,
    totalUsers: 0,
    upcomingEvents: 0
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setIsLoading(true);
        
        // Fetch events
        await fetchEvents({ limit: 100 });
        
        // Fetch users (admin only)
        const usersResponse = await usersAPI.getUsers();
        
        // Calculate stats
        const now = new Date();
        const publishedEvents = events.filter(event => event.status === 'published');
        const upcomingEvents = events.filter(event => 
          new Date(event.startDate) > now && event.status === 'published'
        );
        
        setStats({
          totalEvents: events.length,
          publishedEvents: publishedEvents.length,
          totalUsers: usersResponse.data.pagination.total,
          upcomingEvents: upcomingEvents.length
        });
      } catch (error) {
        console.error('Failed to load dashboard data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadDashboardData();
  }, [fetchEvents, events.length]);

  const currentPath = location.pathname;
  const isOverview = currentPath === '/admin' || currentPath === '/admin/';

  const navItems = [
    { path: '/admin', label: 'Overview', icon: BarChart3 },
    { path: '/admin/events', label: 'Events', icon: Calendar },
    { path: '/admin/users', label: 'Users', icon: Users },
    { path: '/admin/settings', label: 'Settings', icon: Settings },
  ];

  const statCards = [
    {
      icon: Calendar,
      value: stats.totalEvents,
      label: 'Total Events',
      color: '#3b82f6',
      bgColor: '#dbeafe'
    },
    {
      icon: Calendar,
      value: stats.publishedEvents,
      label: 'Published Events',
      color: '#10b981',
      bgColor: '#d1fae5'
    },
    {
      icon: Users,
      value: stats.totalUsers,
      label: 'Total Users',
      color: '#f59e0b',
      bgColor: '#fef3c7'
    },
    {
      icon: Calendar,
      value: stats.upcomingEvents,
      label: 'Upcoming Events',
      color: '#8b5cf6',
      bgColor: '#ede9fe'
    }
  ];

  return (
    <AdminContainer>
      <AdminHeader>
        <AdminTitle>Admin Dashboard</AdminTitle>
        <AdminSubtitle>
          Manage your church events, users, and system settings
        </AdminSubtitle>
      </AdminHeader>

      <AdminNav>
        {navItems.map((item) => (
          <NavLink
            key={item.path}
            to={item.path}
            active={currentPath === item.path}
          >
            <item.icon size={18} />
            {item.label}
          </NavLink>
        ))}
      </AdminNav>

      <Routes>
        <Route path="/" element={
          <>
            {isLoading ? (
              <EmptyState>Loading dashboard data...</EmptyState>
            ) : (
              <>
                <StatsGrid>
                  {statCards.map((stat, index) => (
                    <StatCard
                      key={index}
                      color={stat.color}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                    >
                      <StatIcon color={stat.bgColor} iconColor={stat.color}>
                        <stat.icon size={24} />
                      </StatIcon>
                      <StatValue>{stat.value}</StatValue>
                      <StatLabel>{stat.label}</StatLabel>
                    </StatCard>
                  ))}
                </StatsGrid>

                <ContentArea>
                  <h2>Recent Activity</h2>
                  <p>Dashboard overview content will be displayed here.</p>
                  <EmptyState>
                    <h3>Welcome to the Admin Dashboard</h3>
                    <p>Use the navigation above to manage events, users, and settings.</p>
                  </EmptyState>
                </ContentArea>
              </>
            )}
          </>
        } />
        
        <Route path="/events" element={
          <ContentArea>
            <h2>Event Management</h2>
            <p>Advanced event management features will be available here.</p>
            <EmptyState>
              <Calendar size={48} />
              <h3>Event Management</h3>
              <p>Manage all church events from this section.</p>
            </EmptyState>
          </ContentArea>
        } />
        
        <Route path="/users" element={
          <ContentArea>
            <h2>User Management</h2>
            <p>User management features will be available here.</p>
            <EmptyState>
              <Users size={48} />
              <h3>User Management</h3>
              <p>Create and manage user accounts for editors and administrators.</p>
            </EmptyState>
          </ContentArea>
        } />
        
        <Route path="/settings" element={
          <ContentArea>
            <h2>System Settings</h2>
            <p>System configuration options will be available here.</p>
            <EmptyState>
              <Settings size={48} />
              <h3>System Settings</h3>
              <p>Configure system-wide settings and preferences.</p>
            </EmptyState>
          </ContentArea>
        } />
      </Routes>
    </AdminContainer>
  );
};

export default AdminDashboard;
