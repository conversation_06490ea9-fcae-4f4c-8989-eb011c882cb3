import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { motion } from "framer-motion";
import {
  X,
  Save,
  Calendar,
  Clock,
  MapPin,
  Users,
  Tag,
  Paperclip,
  User,
  Mail,
  Phone,
  Repeat,
} from "lucide-react";
import {
  EVENT_CATEGORIES,
  EVENT_PRIORITIES,
  EVENT_STATUSES,
  DEFAULT_EVENT_FORM,
} from "../../utils/constants";
import {
  getCurrentDate,
  getCurrentTime,
  formatDateForInput,
} from "../../utils/dateUtils";
import FileUpload from "../Common/FileUpload";
import { eventsAPI } from "../../api/events";

const FormOverlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: ${({ theme }) => theme.zIndex.modal};
  padding: ${({ theme }) => theme.spacing[4]};
`;

const FormContainer = styled(motion.div)`
  background: ${({ theme }) => theme.colors.background.primary};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  box-shadow: ${({ theme }) => theme.shadows["2xl"]};
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
`;

const FormHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${({ theme }) => theme.spacing[6]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
`;

const FormTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes["2xl"]};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`;

const CloseButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  background: ${({ theme }) => theme.colors.background.secondary};
  color: ${({ theme }) => theme.colors.text.secondary};
  transition: all ${({ theme }) => theme.transitions.fast};

  &:hover {
    background: ${({ theme }) => theme.colors.error[100]};
    color: ${({ theme }) => theme.colors.error[600]};
  }
`;

const FormBody = styled.div`
  padding: ${({ theme }) => theme.spacing[6]};
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[6]};

  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    grid-template-columns: 1fr;
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};

  &.full-width {
    grid-column: 1 / -1;
  }
`;

const Label = styled.label`
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const Input = styled.input`
  padding: ${({ theme }) => theme.spacing[3]};
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSizes.base};
  transition: all ${({ theme }) => theme.transitions.fast};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary[500]};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.primary[100]};
  }

  &:invalid {
    border-color: ${({ theme }) => theme.colors.error[500]};
  }
`;

const TextArea = styled.textarea`
  padding: ${({ theme }) => theme.spacing[3]};
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSizes.base};
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
  transition: all ${({ theme }) => theme.transitions.fast};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary[500]};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.primary[100]};
  }
`;

const Select = styled.select`
  padding: ${({ theme }) => theme.spacing[3]};
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSizes.base};
  background: ${({ theme }) => theme.colors.background.primary};
  transition: all ${({ theme }) => theme.transitions.fast};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary[500]};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.primary[100]};
  }
`;

const CheckboxGroup = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const Checkbox = styled.input`
  width: 18px;
  height: 18px;
  accent-color: ${({ theme }) => theme.colors.primary[500]};
`;

const FormActions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[6]};
  border-top: 1px solid ${({ theme }) => theme.colors.border.light};
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[6]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  transition: all ${({ theme }) => theme.transitions.fast};

  &.primary {
    background: ${({ theme }) => theme.colors.primary[600]};
    color: white;

    &:hover {
      background: ${({ theme }) => theme.colors.primary[700]};
    }

    &:disabled {
      background: ${({ theme }) => theme.colors.gray[400]};
      cursor: not-allowed;
    }
  }

  &.secondary {
    background: ${({ theme }) => theme.colors.background.secondary};
    color: ${({ theme }) => theme.colors.text.primary};
    border: 1px solid ${({ theme }) => theme.colors.border.medium};

    &:hover {
      background: ${({ theme }) => theme.colors.background.tertiary};
    }
  }
`;

const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors.error[600]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  margin-top: ${({ theme }) => theme.spacing[1]};
`;

const CheckboxContainer = styled.label`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  cursor: pointer;

  input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: ${({ theme }) => theme.colors.primary[600]};
  }

  span {
    font-size: ${({ theme }) => theme.fontSizes.sm};
    color: ${({ theme }) => theme.colors.text.secondary};
  }
`;

const DaysOfWeekContainer = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[1]};
  flex-wrap: wrap;
`;

const DayButton = styled.button`
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background: ${({ active, theme }) =>
    active ? theme.colors.primary[600] : theme.colors.background.secondary};
  color: ${({ active, theme }) =>
    active ? "white" : theme.colors.text.primary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${({ active, theme }) =>
      active ? theme.colors.primary[700] : theme.colors.background.tertiary};
  }
`;

const EventForm = ({
  event,
  selectedDate,
  isOpen,
  onClose,
  onSubmit,
  isLoading,
}) => {
  const [formData, setFormData] = useState(DEFAULT_EVENT_FORM);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (event) {
      setFormData({
        ...event,
        startDate: event.startDate ? event.startDate.split("T")[0] : "",
        endDate: event.endDate ? event.endDate.split("T")[0] : "",
        recurrence: {
          pattern: event.recurrence?.pattern || "weekly",
          interval: event.recurrence?.interval || 1,
          daysOfWeek: event.recurrence?.daysOfWeek || [],
          endDate: event.recurrence?.endDate
            ? event.recurrence.endDate.split("T")[0]
            : "",
        },
        pointOfContact: {
          name: event.pointOfContact?.name || "",
          email: event.pointOfContact?.email || "",
          phone: event.pointOfContact?.phone || "",
        },
      });
    } else {
      // Use selectedDate if provided, otherwise use current date
      const dateToUse = selectedDate
        ? formatDateForInput(selectedDate)
        : getCurrentDate();

      setFormData({
        ...DEFAULT_EVENT_FORM,
        startDate: dateToUse,
        endDate: dateToUse,
        startTime: getCurrentTime(),
      });
    }
  }, [event, selectedDate]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (name.includes(".")) {
      const [parent, child] = name.split(".");
      setFormData((prev) => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === "checkbox" ? checked : value,
        },
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: type === "checkbox" ? checked : value,
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handleFileChange = (files) => {
    setFormData((prev) => ({
      ...prev,
      attachments: files,
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = "Title is required";
    }

    if (!formData.description.trim()) {
      newErrors.description = "Description is required";
    }

    if (!formData.startDate) {
      newErrors.startDate = "Start date is required";
    }

    if (!formData.endDate) {
      newErrors.endDate = "End date is required";
    }

    if (
      formData.startDate &&
      formData.endDate &&
      formData.startDate > formData.endDate
    ) {
      newErrors.endDate = "End date must be after start date";
    }

    if (!formData.location.name.trim()) {
      newErrors["location.name"] = "Location is required";
    }

    // Validate point of contact email if provided
    if (formData.pointOfContact.email && formData.pointOfContact.email.trim()) {
      const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
      if (!emailRegex.test(formData.pointOfContact.email)) {
        newErrors["pointOfContact.email"] =
          "Please enter a valid email address";
      }
    }

    // Validate point of contact phone if provided
    if (formData.pointOfContact.phone && formData.pointOfContact.phone.trim()) {
      if (formData.pointOfContact.phone.length > 20) {
        newErrors["pointOfContact.phone"] =
          "Phone number cannot exceed 20 characters";
      }
    }

    // Validate point of contact name if provided
    if (formData.pointOfContact.name && formData.pointOfContact.name.trim()) {
      if (formData.pointOfContact.name.length > 100) {
        newErrors["pointOfContact.name"] =
          "Contact name cannot exceed 100 characters";
      }
    }

    // Validate recurrence fields if recurring
    if (formData.isRecurring) {
      if (!formData.recurrence.pattern) {
        newErrors["recurrence.pattern"] = "Recurrence pattern is required";
      }

      if (!formData.recurrence.interval || formData.recurrence.interval < 1) {
        newErrors["recurrence.interval"] = "Interval must be at least 1";
      }

      if (formData.recurrence.interval > 52) {
        newErrors["recurrence.interval"] = "Interval cannot exceed 52";
      }

      if (
        formData.recurrence.pattern === "weekly" &&
        formData.recurrence.daysOfWeek.length === 0
      ) {
        newErrors["recurrence.daysOfWeek"] =
          "Please select at least one day of the week";
      }

      if (
        formData.recurrence.endDate &&
        formData.recurrence.endDate <= formData.startDate
      ) {
        newErrors["recurrence.endDate"] =
          "Recurrence end date must be after start date";
      }
    }

    // Validate attachments
    if (formData.attachments && formData.attachments.length > 0) {
      const maxFiles = 10;
      const maxFileSize = 5 * 1024 * 1024; // 5MB

      if (formData.attachments.length > maxFiles) {
        newErrors.attachments = `Too many files. Maximum ${maxFiles} files allowed.`;
      }

      // Check each attachment
      for (let i = 0; i < formData.attachments.length; i++) {
        const file = formData.attachments[i];

        if (file instanceof File) {
          if (file.size > maxFileSize) {
            newErrors.attachments = `File "${file.name}" is too large. Maximum size is 5MB.`;
            break;
          }

          if (file.size === 0) {
            newErrors.attachments = `File "${file.name}" is empty.`;
            break;
          }
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Prepare form data for submission
    const submissionData = { ...formData };

    // Handle recurrence end date if present
    if (formData.isRecurring && formData.recurrence.endDate) {
      submissionData.recurrence = {
        ...formData.recurrence,
      };
    }

    // Handle file uploads if there are new files
    const newFiles = formData.attachments.filter(
      (file) => file instanceof File
    );
    const existingFiles = formData.attachments.filter(
      (file) => !(file instanceof File)
    );

    if (newFiles.length > 0) {
      try {
        // Upload new files
        const uploadedFiles = await uploadFiles(newFiles);
        submissionData.attachments = [...existingFiles, ...uploadedFiles];
      } catch (error) {
        console.error("File upload failed:", error);
        const errorMessage =
          error.response?.data?.message || "File upload failed";
        setErrors((prev) => ({ ...prev, attachments: errorMessage }));
        return; // Don't submit the form if file upload fails
      }
    } else {
      submissionData.attachments = existingFiles;
    }

    onSubmit(submissionData);
  };

  const uploadFiles = async (files) => {
    const result = await eventsAPI.uploadFiles(files);
    return result.data.files;
  };

  if (!isOpen) return null;

  return (
    <FormOverlay
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <FormContainer
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        onClick={(e) => e.stopPropagation()}
      >
        <FormHeader>
          <FormTitle>{event ? "Edit Event" : "Create New Event"}</FormTitle>
          <CloseButton onClick={onClose}>
            <X size={20} />
          </CloseButton>
        </FormHeader>

        <form onSubmit={handleSubmit}>
          <FormBody>
            <FormGrid>
              <FormGroup className="full-width">
                <Label>
                  <Tag size={16} />
                  Event Title *
                </Label>
                <Input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  placeholder="Enter event title"
                  required
                />
                {errors.title && <ErrorMessage>{errors.title}</ErrorMessage>}
              </FormGroup>

              <FormGroup className="full-width">
                <Label>Description *</Label>
                <TextArea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  placeholder="Enter event description"
                  required
                />
                {errors.description && (
                  <ErrorMessage>{errors.description}</ErrorMessage>
                )}
              </FormGroup>

              <FormGroup>
                <Label>
                  <Calendar size={16} />
                  Start Date *
                </Label>
                <Input
                  type="date"
                  name="startDate"
                  value={formData.startDate}
                  onChange={handleChange}
                  required
                />
                {errors.startDate && (
                  <ErrorMessage>{errors.startDate}</ErrorMessage>
                )}
              </FormGroup>

              <FormGroup>
                <Label>
                  <Calendar size={16} />
                  End Date *
                </Label>
                <Input
                  type="date"
                  name="endDate"
                  value={formData.endDate}
                  onChange={handleChange}
                  required
                />
                {errors.endDate && (
                  <ErrorMessage>{errors.endDate}</ErrorMessage>
                )}
              </FormGroup>

              <FormGroup>
                <Label>
                  <Clock size={16} />
                  Start Time *
                </Label>
                <Input
                  type="time"
                  name="startTime"
                  value={formData.startTime}
                  onChange={handleChange}
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label>
                  <Clock size={16} />
                  End Time *
                </Label>
                <Input
                  type="time"
                  name="endTime"
                  value={formData.endTime}
                  onChange={handleChange}
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label>
                  <MapPin size={16} />
                  Location *
                </Label>
                <Input
                  type="text"
                  name="location.name"
                  value={formData.location.name}
                  onChange={handleChange}
                  placeholder="Enter location"
                  required
                />
                {errors["location.name"] && (
                  <ErrorMessage>{errors["location.name"]}</ErrorMessage>
                )}
              </FormGroup>

              <FormGroup>
                <Label>
                  <User size={16} />
                  Point of Contact
                </Label>
                <Input
                  type="text"
                  name="pointOfContact.name"
                  value={formData.pointOfContact.name}
                  onChange={handleChange}
                  placeholder="Contact person name"
                />
                {errors["pointOfContact.name"] && (
                  <ErrorMessage>{errors["pointOfContact.name"]}</ErrorMessage>
                )}
              </FormGroup>

              <FormGroup>
                <Label>
                  <Mail size={16} />
                  Contact Email
                </Label>
                <Input
                  type="email"
                  name="pointOfContact.email"
                  value={formData.pointOfContact.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                />
                {errors["pointOfContact.email"] && (
                  <ErrorMessage>{errors["pointOfContact.email"]}</ErrorMessage>
                )}
              </FormGroup>

              <FormGroup>
                <Label>
                  <Phone size={16} />
                  Contact Phone
                </Label>
                <Input
                  type="tel"
                  name="pointOfContact.phone"
                  value={formData.pointOfContact.phone}
                  onChange={handleChange}
                  placeholder="(*************"
                />
                {errors["pointOfContact.phone"] && (
                  <ErrorMessage>{errors["pointOfContact.phone"]}</ErrorMessage>
                )}
              </FormGroup>

              <FormGroup>
                <Label>
                  <Repeat size={16} />
                  Recurring Event
                </Label>
                <CheckboxContainer>
                  <input
                    type="checkbox"
                    name="isRecurring"
                    checked={formData.isRecurring}
                    onChange={handleChange}
                  />
                  <span>This is a recurring event</span>
                </CheckboxContainer>
              </FormGroup>

              {formData.isRecurring && (
                <>
                  <FormGroup>
                    <Label>Recurrence Pattern</Label>
                    <Select
                      name="recurrence.pattern"
                      value={formData.recurrence.pattern}
                      onChange={handleChange}
                    >
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                      <option value="yearly">Yearly</option>
                    </Select>
                    {errors["recurrence.pattern"] && (
                      <ErrorMessage>
                        {errors["recurrence.pattern"]}
                      </ErrorMessage>
                    )}
                  </FormGroup>

                  <FormGroup>
                    <Label>Repeat Every</Label>
                    <Input
                      type="number"
                      name="recurrence.interval"
                      value={formData.recurrence.interval}
                      onChange={handleChange}
                      min="1"
                      max="52"
                      placeholder="1"
                    />
                    <small
                      style={{
                        color: "#666",
                        fontSize: "0.85em",
                        marginTop: "4px",
                        display: "block",
                      }}
                    >
                      {formData.recurrence.pattern === "daily" && "day(s)"}
                      {formData.recurrence.pattern === "weekly" && "week(s)"}
                      {formData.recurrence.pattern === "monthly" && "month(s)"}
                      {formData.recurrence.pattern === "yearly" && "year(s)"}
                    </small>
                    {errors["recurrence.interval"] && (
                      <ErrorMessage>
                        {errors["recurrence.interval"]}
                      </ErrorMessage>
                    )}
                  </FormGroup>

                  {formData.recurrence.pattern === "weekly" && (
                    <FormGroup>
                      <Label>Days of Week</Label>
                      <DaysOfWeekContainer>
                        {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map(
                          (day, index) => (
                            <DayButton
                              key={day}
                              type="button"
                              active={formData.recurrence.daysOfWeek.includes(
                                index
                              )}
                              onClick={() => {
                                const days = [
                                  ...formData.recurrence.daysOfWeek,
                                ];
                                const dayIndex = days.indexOf(index);
                                if (dayIndex > -1) {
                                  days.splice(dayIndex, 1);
                                } else {
                                  days.push(index);
                                }
                                setFormData((prev) => ({
                                  ...prev,
                                  recurrence: {
                                    ...prev.recurrence,
                                    daysOfWeek: days.sort(),
                                  },
                                }));
                              }}
                            >
                              {day}
                            </DayButton>
                          )
                        )}
                      </DaysOfWeekContainer>
                      {errors["recurrence.daysOfWeek"] && (
                        <ErrorMessage>
                          {errors["recurrence.daysOfWeek"]}
                        </ErrorMessage>
                      )}
                    </FormGroup>
                  )}

                  <FormGroup>
                    <Label>Recurrence End Date</Label>
                    <Input
                      type="date"
                      name="recurrence.endDate"
                      value={formData.recurrence.endDate}
                      onChange={handleChange}
                      min={formData.startDate}
                    />
                    <small
                      style={{
                        color: "#666",
                        fontSize: "0.85em",
                        marginTop: "4px",
                        display: "block",
                      }}
                    >
                      Leave empty for no end date
                    </small>
                    {errors["recurrence.endDate"] && (
                      <ErrorMessage>
                        {errors["recurrence.endDate"]}
                      </ErrorMessage>
                    )}
                  </FormGroup>
                </>
              )}

              <FormGroup>
                <Label>Category</Label>
                <Select
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                >
                  {EVENT_CATEGORIES.map((category) => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </Select>
              </FormGroup>

              <FormGroup>
                <Label>Priority</Label>
                <Select
                  name="priority"
                  value={formData.priority}
                  onChange={handleChange}
                >
                  {EVENT_PRIORITIES.map((priority) => (
                    <option key={priority.value} value={priority.value}>
                      {priority.label}
                    </option>
                  ))}
                </Select>
              </FormGroup>

              <FormGroup>
                <Label>Status</Label>
                <Select
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                >
                  {EVENT_STATUSES.map((status) => (
                    <option key={status.value} value={status.value}>
                      {status.label}
                    </option>
                  ))}
                </Select>
              </FormGroup>

              <FormGroup>
                <Label>
                  <Users size={16} />
                  Expected Attendees
                </Label>
                <Input
                  type="number"
                  name="attendees.expected"
                  value={formData.attendees.expected}
                  onChange={handleChange}
                  min="0"
                  placeholder="0"
                />
              </FormGroup>

              <FormGroup>
                <CheckboxGroup>
                  <Checkbox
                    type="checkbox"
                    name="isPublic"
                    checked={formData.isPublic}
                    onChange={handleChange}
                  />
                  <Label>Public Event</Label>
                </CheckboxGroup>
              </FormGroup>

              <FormGroup className="full-width">
                <Label>
                  <Paperclip size={16} />
                  Documents & Attachments
                </Label>
                <FileUpload
                  files={formData.attachments}
                  onChange={handleFileChange}
                  maxFiles={10}
                  maxSize={5 * 1024 * 1024} // 5MB
                  disabled={isLoading}
                />
                {errors.attachments && (
                  <ErrorMessage>{errors.attachments}</ErrorMessage>
                )}
              </FormGroup>
            </FormGrid>
          </FormBody>

          <FormActions>
            <Button type="button" className="secondary" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" className="primary" disabled={isLoading}>
              <Save size={16} />
              {isLoading
                ? "Saving..."
                : event
                ? "Update Event"
                : "Create Event"}
            </Button>
          </FormActions>
        </form>
      </FormContainer>
    </FormOverlay>
  );
};

export default EventForm;
