import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { motion, AnimatePresence } from "framer-motion";
import {
  ChevronLeft,
  ChevronRight,
  Calendar as CalendarIcon,
  List,
  Grid,
} from "lucide-react";
import MonthView from "./MonthView";
import WeekView from "./WeekView";
import DayView from "./DayView";
import AgendaView from "./AgendaView";
import { useEventsStore } from "../../store";
import { formatDate, getMonthName } from "../../utils/dateUtils";

const CalendarContainer = styled.div`
  background: ${({ theme }) => theme.colors.background.primary};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  box-shadow: ${({ theme }) => theme.shadows.md};
  overflow: hidden;
  border: 1px solid ${({ theme }) => theme.colors.border.light};

  /* Desktop web view styling */
  @media (min-width: 769px) {
    border-radius: ${({ theme }) => theme.borderRadius.xl};
    box-shadow: ${({ theme }) => theme.shadows.lg};
    border: 1px solid ${({ theme }) => theme.colors.border.medium};
  }

  /* Mobile native feel */
  @media (max-width: 768px) {
    border-radius: ${({ theme }) => theme.borderRadius.md};
    box-shadow: ${({ theme }) => theme.shadows.sm};
    border: none; /* Remove all borders for clean mobile look */
    margin: 0 -${({ theme }) => theme.spacing[2]};
  }
`;

const CalendarHeader = styled.div`
  background: ${({ theme }) => theme.colors.primary[50]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  padding: ${({ theme }) => theme.spacing[4]};
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing[4]};
  position: relative;

  /* Add subtle pattern */
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
    pointer-events: none;
  }

  /* Desktop web view - professional header */
  @media (min-width: 769px) {
    padding: ${({ theme }) => theme.spacing[5]}
      ${({ theme }) => theme.spacing[6]};
    background: linear-gradient(
      135deg,
      ${({ theme }) => theme.colors.primary[50]} 0%,
      ${({ theme }) => theme.colors.primary[25]} 100%
    );
    border-bottom: 2px solid ${({ theme }) => theme.colors.primary[200]};
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  /* Mobile native feel - compact header */
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing[2]}
      ${({ theme }) => theme.spacing[3]};
    gap: ${({ theme }) => theme.spacing[2]};
    background: ${({ theme }) => theme.colors.background.primary};
    border-bottom: none; /* Remove border for clean mobile look */
    position: sticky;
    top: 0;
    z-index: 10;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    flex-direction: column;
    align-items: stretch;
  }
`;

const NavigationSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[4]};
  position: relative;
  z-index: 1;

  /* Desktop web view - spacious navigation */
  @media (min-width: 769px) {
    gap: ${({ theme }) => theme.spacing[5]};
  }

  /* Mobile native feel - compact navigation */
  @media (max-width: 768px) {
    gap: ${({ theme }) => theme.spacing[2]};
    flex: 1;
    justify-content: space-between;
    align-items: center;
    order: 1;
  }
`;

const NavButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background: ${({ theme }) => theme.colors.background.primary};
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  transition: all ${({ theme }) => theme.transitions.fast};
  cursor: pointer;

  &:hover {
    background: ${({ theme }) => theme.colors.primary[50]};
    border-color: ${({ theme }) => theme.colors.primary[200]};
    color: ${({ theme }) => theme.colors.primary[600]};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* Desktop web view - larger buttons with hover effects */
  @media (min-width: 769px) {
    width: 44px;
    height: 44px;
    border-radius: ${({ theme }) => theme.borderRadius.lg};

    &:hover {
      transform: translateY(-1px);
      box-shadow: ${({ theme }) => theme.shadows.sm};
    }

    &:active {
      transform: translateY(0);
    }
  }

  /* Mobile native feel - touch-optimized buttons */
  @media (max-width: 768px) {
    width: 36px;
    height: 36px;
    border-radius: ${({ theme }) => theme.borderRadius.sm};
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      transform: none; /* Disable hover effects on mobile */
    }

    &:active {
      background: ${({ theme }) => theme.colors.primary[100]};
      transform: scale(0.95);
    }
  }
`;

const CurrentDate = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes["3xl"]};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  letter-spacing: -0.025em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;

  /* Add spiritual emphasis */
  &::before {
    content: "🗓️";
    position: absolute;
    left: -2rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.7em;
    opacity: 0.8;
  }

  /* Desktop web view - larger title */
  @media (min-width: 769px) {
    font-size: ${({ theme }) => theme.fontSizes["4xl"]};
    letter-spacing: -0.05em;
    &::before {
      left: -2.5rem;
      font-size: 0.8em;
    }
  }

  /* Mobile native feel - clean, readable title */
  @media (max-width: 768px) {
    font-size: ${({ theme }) => theme.fontSizes.lg};
    font-weight: ${({ theme }) => theme.fontWeights.semibold};
    letter-spacing: 0;
    text-align: center;
    flex: 1;
    margin: 0;
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    background-clip: unset;
    color: ${({ theme }) => theme.colors.text.primary};
    text-shadow: none;
    line-height: 1.2;

    &::before {
      display: none;
    }
  }
`;

const ViewControls = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
  background: ${({ theme }) => theme.colors.background.primary};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[1]};
  position: relative;
  z-index: 1;

  /* Desktop web view - larger controls */
  @media (min-width: 769px) {
    gap: ${({ theme }) => theme.spacing[2]};
    padding: ${({ theme }) => theme.spacing[2]};
    border-radius: ${({ theme }) => theme.borderRadius.xl};
    box-shadow: ${({ theme }) => theme.shadows.sm};
  }

  /* Mobile native feel - compact controls */
  @media (max-width: 768px) {
    gap: 2px;
    padding: 2px;
    border-radius: ${({ theme }) => theme.borderRadius.md};
    background: ${({ theme }) => theme.colors.background.secondary};
    order: 2;
    margin-top: ${({ theme }) => theme.spacing[2]};
    align-self: center;
  }
`;

const ViewButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background: ${({ active, theme }) =>
    active ? theme.colors.primary[600] : "transparent"};
  color: ${({ active, theme }) =>
    active ? "white" : theme.colors.text.secondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  transition: all ${({ theme }) => theme.transitions.fast};
  border: none;
  cursor: pointer;

  &:hover {
    background: ${({ active, theme }) =>
      active ? theme.colors.primary[700] : theme.colors.background.secondary};
  }

  /* Desktop web view - larger buttons with text */
  @media (min-width: 769px) {
    padding: ${({ theme }) => theme.spacing[3]}
      ${({ theme }) => theme.spacing[4]};
    font-size: ${({ theme }) => theme.fontSizes.base};
    border-radius: ${({ theme }) => theme.borderRadius.lg};

    &:hover {
      transform: translateY(-1px);
      box-shadow: ${({ theme }) => theme.shadows.sm};
    }
  }

  /* Mobile native feel - icon-only buttons */
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing[2]};
    border-radius: ${({ theme }) => theme.borderRadius.sm};
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;

    span {
      display: none;
    }

    &:hover {
      transform: none;
    }

    &:active {
      transform: scale(0.95);
    }
  }
`;

const TodayButton = styled.button`
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[4]};
  background: ${({ theme }) => theme.colors.secondary[500]};
  color: white;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  transition: all ${({ theme }) => theme.transitions.fast};
  border: none;
  cursor: pointer;

  &:hover {
    background: ${({ theme }) => theme.colors.secondary[600]};
  }

  /* Desktop web view - larger today button */
  @media (min-width: 769px) {
    padding: ${({ theme }) => theme.spacing[3]}
      ${({ theme }) => theme.spacing[5]};
    font-size: ${({ theme }) => theme.fontSizes.base};
    border-radius: ${({ theme }) => theme.borderRadius.lg};

    &:hover {
      transform: translateY(-1px);
      box-shadow: ${({ theme }) => theme.shadows.md};
    }
  }

  /* Mobile native feel - compact today button */
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing[2]}
      ${({ theme }) => theme.spacing[3]};
    font-size: ${({ theme }) => theme.fontSizes.sm};
    border-radius: ${({ theme }) => theme.borderRadius.md};
    font-weight: ${({ theme }) => theme.fontWeights.medium};
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);

    &:hover {
      transform: translateY(-50%);
    }

    &:active {
      transform: translateY(-50%) scale(0.95);
    }
  }
`;

// Mobile-specific title component - REMOVED to avoid double headers
// The MobileHeader already shows "Calendar" title
const MobileCalendarTitle = styled.h1`
  display: none; /* Completely hidden to avoid double headers */
`;

const CalendarContent = styled.div`
  min-height: 600px;
  position: relative;

  /* Desktop web view - larger calendar content */
  @media (min-width: 769px) {
    min-height: 700px;
    padding: ${({ theme }) => theme.spacing[2]};
  }

  /* Mobile native feel - optimized content height */
  @media (max-width: 768px) {
    min-height: 400px;
    padding: 0;
    overflow-x: hidden;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const CalendarView = ({ onEventClick, onDateClick }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState("month");
  const { calendarEvents, isLoading, fetchCalendarEvents } = useEventsStore();

  useEffect(() => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;
    fetchCalendarEvents(year, month);
  }, [currentDate, fetchCalendarEvents]);

  const navigateDate = (direction) => {
    const newDate = new Date(currentDate);

    switch (view) {
      case "month":
        newDate.setMonth(newDate.getMonth() + direction);
        break;
      case "week":
        newDate.setDate(newDate.getDate() + direction * 7);
        break;
      case "day":
        newDate.setDate(newDate.getDate() + direction);
        break;
      default:
        newDate.setMonth(newDate.getMonth() + direction);
    }

    setCurrentDate(newDate);
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  const getDateTitle = () => {
    switch (view) {
      case "month":
        return `${getMonthName(
          currentDate.getMonth()
        )} ${currentDate.getFullYear()}`;
      case "week":
        const weekStart = new Date(currentDate);
        weekStart.setDate(currentDate.getDate() - currentDate.getDay());
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        return `${formatDate(weekStart, "MMM dd")} - ${formatDate(
          weekEnd,
          "MMM dd, yyyy"
        )}`;
      case "day":
        return formatDate(currentDate, "EEEE, MMMM dd, yyyy");
      case "agenda":
        return "Upcoming Events";
      default:
        return "";
    }
  };

  const viewComponents = {
    month: MonthView,
    week: WeekView,
    day: DayView,
    agenda: AgendaView,
  };

  const ViewComponent = viewComponents[view];

  const viewButtons = [
    { key: "month", label: "Month", icon: Grid },
    { key: "week", label: "Week", icon: CalendarIcon },
    { key: "day", label: "Day", icon: CalendarIcon },
    { key: "agenda", label: "Agenda", icon: List },
  ];

  return (
    <CalendarContainer>
      <MobileCalendarTitle>Church Calendar</MobileCalendarTitle>
      <CalendarHeader>
        <NavigationSection>
          <NavButton onClick={() => navigateDate(-1)}>
            <ChevronLeft size={20} />
          </NavButton>
          <CurrentDate>{getDateTitle()}</CurrentDate>
          <NavButton onClick={() => navigateDate(1)}>
            <ChevronRight size={20} />
          </NavButton>
          <TodayButton onClick={goToToday}>Today</TodayButton>
        </NavigationSection>

        <ViewControls>
          {viewButtons.map(({ key, label, icon: Icon }) => (
            <ViewButton
              key={key}
              active={view === key}
              onClick={() => setView(key)}
            >
              <Icon size={16} />
              <span>{label}</span>
            </ViewButton>
          ))}
        </ViewControls>
      </CalendarHeader>

      <CalendarContent>
        <AnimatePresence mode="wait">
          {isLoading ? (
            <LoadingContainer>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                Loading events...
              </motion.div>
            </LoadingContainer>
          ) : (
            <motion.div
              key={view}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <ViewComponent
                currentDate={currentDate}
                events={calendarEvents}
                onEventClick={onEventClick}
                onDateClick={onDateClick}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </CalendarContent>
    </CalendarContainer>
  );
};

export default CalendarView;
