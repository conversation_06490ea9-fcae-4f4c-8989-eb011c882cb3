const PDFDocument = require("pdfkit");
const { Document, Packer, Paragraph, TextRun, AlignmentType } = require("docx");

// Format date for display
const formatDate = (date) => {
  const d = new Date(date);
  return d.toLocaleDateString("en-US", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

// Format time for display
const formatTime = (time) => {
  if (!time) return "";
  const [hours, minutes] = time.split(":");
  const hour = parseInt(hours, 10);
  const ampm = hour >= 12 ? "PM" : "AM";
  const displayHour = hour % 12 || 12;
  return `${displayHour}:${minutes} ${ampm}`;
};

// Get recurrence info
const getRecurrenceInfo = (event) => {
  if (!event.isRecurring || !event.recurrence) return "One-time event";
  return `Recurring: ${event.recurrence.pattern}`;
};

// Generate PDF - Simple Clean Agenda
const generatePDF = (events) => {
  return new Promise((resolve, reject) => {
    try {
      const doc = new PDFDocument({
        margin: 50,
        size: "A4",
      });

      const chunks = [];

      doc.on("data", (chunk) => chunks.push(chunk));
      doc.on("end", () => {
        const buffer = Buffer.concat(chunks);
        resolve(buffer);
      });
      doc.on("error", reject);

      // Sort events by date
      const sortedEvents = events.sort(
        (a, b) => new Date(a.startDate) - new Date(b.startDate)
      );

      // Simple Header
      doc.fontSize(24).text("Grace Bible Fellowship", { align: "center" });
      doc.fontSize(18).text("Event Calendar", { align: "center" });
      doc.fontSize(12).text(`Generated: ${new Date().toLocaleDateString()}`, {
        align: "center",
      });
      doc.moveDown(2);

      // Simple event listing
      sortedEvents.forEach((event, index) => {
        if (index > 0) {
          doc.moveDown(1);
        }

        // Check for page break
        if (doc.y > 700) {
          doc.addPage();
        }

        // Event date
        doc.fontSize(14).text(`Date: ${formatDate(event.startDate)}`);

        // Event title
        doc.fontSize(16).text(event.title || "Event", { continued: false });

        // Time
        if (event.startTime && event.endTime) {
          doc
            .fontSize(12)
            .text(
              `Time: ${formatTime(event.startTime)} - ${formatTime(
                event.endTime
              )}`
            );
        }

        // Location
        if (event.location?.name) {
          doc.fontSize(12).text(`Location: ${event.location.name}`);
        }

        // Description
        if (event.description) {
          doc.fontSize(11).text(`Description: ${event.description}`);
        }

        // Contact
        if (event.pointOfContact?.name) {
          doc.fontSize(11).text(`Contact: ${event.pointOfContact.name}`);
        }

        doc.moveDown(0.5);
      });

      doc.end();
    } catch (error) {
      reject(error);
    }
  });
};

// Generate Word document - Simple Clean Format
const generateWord = async (events) => {
  try {
    // Sort events by date
    const sortedEvents = events.sort(
      (a, b) => new Date(a.startDate) - new Date(b.startDate)
    );

    const doc = new Document({
      sections: [
        {
          properties: {},
          children: [
            // Header
            new Paragraph({
              children: [
                new TextRun({
                  text: "Grace Bible Fellowship",
                  bold: true,
                  size: 32,
                }),
              ],
              alignment: AlignmentType.CENTER,
              spacing: { after: 200 },
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: "Event Calendar",
                  size: 24,
                }),
              ],
              alignment: AlignmentType.CENTER,
              spacing: { after: 200 },
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: `Generated: ${new Date().toLocaleDateString()}`,
                  size: 16,
                }),
              ],
              alignment: AlignmentType.CENTER,
              spacing: { after: 400 },
            }),

            // Simple event listing
            ...sortedEvents.flatMap((event, index) => [
              // Event title
              new Paragraph({
                children: [
                  new TextRun({
                    text: event.title || "Event",
                    bold: true,
                    size: 20,
                  }),
                ],
                spacing: { before: index > 0 ? 300 : 200, after: 100 },
              }),

              // Date
              new Paragraph({
                children: [
                  new TextRun({
                    text: `Date: ${formatDate(event.startDate)}`,
                    size: 16,
                  }),
                ],
                spacing: { after: 100 },
              }),

              // Time
              ...(event.startTime && event.endTime
                ? [
                    new Paragraph({
                      children: [
                        new TextRun({
                          text: `Time: ${formatTime(
                            event.startTime
                          )} - ${formatTime(event.endTime)}`,
                          size: 16,
                        }),
                      ],
                      spacing: { after: 100 },
                    }),
                  ]
                : []),

              // Location
              ...(event.location?.name
                ? [
                    new Paragraph({
                      children: [
                        new TextRun({
                          text: `Location: ${event.location.name}`,
                          size: 16,
                        }),
                      ],
                      spacing: { after: 100 },
                    }),
                  ]
                : []),

              // Description
              ...(event.description
                ? [
                    new Paragraph({
                      children: [
                        new TextRun({
                          text: `Description: ${event.description}`,
                          size: 14,
                        }),
                      ],
                      spacing: { after: 100 },
                    }),
                  ]
                : []),

              // Contact
              ...(event.pointOfContact?.name
                ? [
                    new Paragraph({
                      children: [
                        new TextRun({
                          text: `Contact: ${event.pointOfContact.name}`,
                          size: 14,
                        }),
                      ],
                      spacing: { after: 200 },
                    }),
                  ]
                : []),
            ]),
          ],
        },
      ],
    });

    return await Packer.toBuffer(doc);
  } catch (error) {
    throw error;
  }
};

module.exports = {
  generatePDF,
  generateWord,
  formatDate,
  formatTime,
  getRecurrenceInfo,
};
