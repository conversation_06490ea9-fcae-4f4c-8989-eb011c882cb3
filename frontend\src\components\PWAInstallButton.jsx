import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

const PWAInstallButton = () => {
  const [deferredPrompt, setDeferredPrompt] = useState(null);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isStandalone, setIsStandalone] = useState(false);
  const [showBanner, setShowBanner] = useState(false);

  useEffect(() => {
    // Check if app is already installed or running in standalone mode
    const checkInstallStatus = () => {
      const standalone =
        window.matchMedia("(display-mode: standalone)").matches ||
        window.navigator.standalone ||
        document.referrer.includes("android-app://");
      setIsStandalone(standalone);

      // Check if app was previously installed
      const wasInstalled = localStorage.getItem("pwa-installed") === "true";
      setIsInstalled(wasInstalled || standalone);
    };

    checkInstallStatus();

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e) => {
      console.log("PWA install prompt available");
      e.preventDefault();
      setDeferredPrompt(e);

      // Show install prompt if not already installed
      if (!isInstalled && !isStandalone) {
        setShowInstallPrompt(true);
        // Show banner after a short delay
        setTimeout(() => setShowBanner(true), 2000);
      }
    };

    // Listen for app installed event
    const handleAppInstalled = () => {
      console.log("PWA was installed");
      setIsInstalled(true);
      setShowInstallPrompt(false);
      setShowBanner(false);
      localStorage.setItem("pwa-installed", "true");
    };

    window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt);
    window.addEventListener("appinstalled", handleAppInstalled);

    return () => {
      window.removeEventListener(
        "beforeinstallprompt",
        handleBeforeInstallPrompt
      );
      window.removeEventListener("appinstalled", handleAppInstalled);
    };
  }, [isInstalled, isStandalone]);

  const handleInstallClick = async () => {
    if (!deferredPrompt) {
      // Fallback for browsers that don't support the install prompt
      showManualInstallInstructions();
      return;
    }

    try {
      // Show the install prompt
      deferredPrompt.prompt();

      // Wait for the user to respond to the prompt
      const { outcome } = await deferredPrompt.userChoice;

      if (outcome === "accepted") {
        console.log("User accepted the install prompt");
        setIsInstalled(true);
        setShowInstallPrompt(false);
        setShowBanner(false);
        localStorage.setItem("pwa-installed", "true");
      } else {
        console.log("User dismissed the install prompt");
      }

      // Clear the deferredPrompt
      setDeferredPrompt(null);
    } catch (error) {
      console.error("Error during installation:", error);
      showManualInstallInstructions();
    }
  };

  const showManualInstallInstructions = () => {
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isAndroid = /Android/.test(navigator.userAgent);
    const isChrome = /Chrome/.test(navigator.userAgent);
    const isEdge = /Edg/.test(navigator.userAgent);
    const isFirefox = /Firefox/.test(navigator.userAgent);

    let instructions = "";
    if (isIOS) {
      instructions =
        '📱 To install this app on your iPhone/iPad:\n\n1. Open this website in Safari\n2. Tap the Share button (⬆️) at the bottom\n3. Scroll down and tap "Add to Home Screen"\n4. Tap "Add" to confirm\n\n✨ The app will appear on your home screen!';
    } else if (isAndroid && isChrome) {
      instructions =
        '📱 To install this app on your Android device:\n\n1. Tap the menu (⋮) in Chrome\n2. Tap "Add to Home screen" or "Install app"\n3. Tap "Add" to confirm\n\n✨ The app will appear on your home screen!';
    } else if (isChrome || isEdge) {
      instructions =
        '💻 To install this app on your computer:\n\n1. Look for the install icon (⬇️) in your browser\'s address bar\n2. Click it and select "Install"\n3. Or click the menu (⋮) and select "Install Grace Bible Fellowship"\n\n✨ The app will be available like any desktop application!';
    } else if (isFirefox) {
      instructions =
        "🦊 Firefox doesn't support automatic installation, but you can:\n\n1. Bookmark this page\n2. Add it to your home screen (mobile)\n3. Or use it as a regular website\n\n✨ You'll still get the full app experience!";
    } else {
      instructions =
        "🌐 To install this app:\n\n1. Look for an install option in your browser's menu\n2. Or bookmark this page for quick access\n3. The app works great in any browser!\n\n✨ Enjoy the full church app experience!";
    }

    // Create a better modal instead of alert
    const modal = document.createElement("div");
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10001;
      padding: 20px;
    `;

    const content = document.createElement("div");
    content.style.cssText = `
      background: white;
      border-radius: 12px;
      padding: 24px;
      max-width: 400px;
      width: 100%;
      text-align: center;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    `;

    content.innerHTML = `
      <h3 style="margin: 0 0 16px 0; color: #2563eb; font-size: 18px;">Install Instructions</h3>
      <p style="margin: 0 0 20px 0; white-space: pre-line; line-height: 1.5; color: #374151;">${instructions}</p>
      <button onclick="this.closest('div').remove()" style="
        background: #2563eb;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 10px 20px;
        font-size: 14px;
        cursor: pointer;
        font-weight: 500;
      ">Got it!</button>
    `;

    modal.appendChild(content);
    document.body.appendChild(modal);

    // Remove modal when clicking outside
    modal.addEventListener("click", (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });
  };

  const dismissBanner = () => {
    setShowBanner(false);
    localStorage.setItem("pwa-banner-dismissed", "true");
  };

  const dismissPrompt = () => {
    setShowInstallPrompt(false);
    localStorage.setItem("pwa-prompt-dismissed", "true");
  };

  // Don't show anything if already installed or in standalone mode
  if (isInstalled || isStandalone) {
    return null;
  }

  return (
    <>
      {/* Install Banner */}
      <AnimatePresence>
        {showBanner && (
          <motion.div
            initial={{ y: -100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -100, opacity: 0 }}
            transition={{ duration: 0.3 }}
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              backgroundColor: "#2563eb",
              color: "white",
              padding: "12px 16px",
              zIndex: 10001,
              boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              gap: "12px",
              fontSize: "14px",
              fontWeight: "500",
            }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                gap: "8px",
                flex: 1,
              }}
            >
              <span style={{ fontSize: "18px" }}>📱</span>
              <span>Install Grace Bible Fellowship app for quick access!</span>
            </div>

            <div style={{ display: "flex", gap: "8px", alignItems: "center" }}>
              <button
                onClick={handleInstallClick}
                style={{
                  backgroundColor: "white",
                  color: "#2563eb",
                  border: "none",
                  borderRadius: "6px",
                  padding: "6px 12px",
                  fontSize: "12px",
                  fontWeight: "600",
                  cursor: "pointer",
                  whiteSpace: "nowrap",
                }}
              >
                Install
              </button>

              <button
                onClick={dismissBanner}
                style={{
                  backgroundColor: "transparent",
                  color: "white",
                  border: "none",
                  cursor: "pointer",
                  padding: "4px",
                  fontSize: "16px",
                  lineHeight: "1",
                }}
              >
                ×
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Floating Install Button */}
      <AnimatePresence>
        {showInstallPrompt && !showBanner && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            style={{
              position: "fixed",
              bottom: "20px",
              right: "20px",
              zIndex: 10000,
            }}
          >
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleInstallClick}
              style={{
                backgroundColor: "#2563eb",
                color: "white",
                border: "none",
                borderRadius: "50px",
                padding: "12px 20px",
                fontSize: "14px",
                fontWeight: "600",
                cursor: "pointer",
                boxShadow: "0 4px 20px rgba(37, 99, 235, 0.3)",
                display: "flex",
                alignItems: "center",
                gap: "8px",
                minHeight: "48px",
                touchAction: "manipulation",
              }}
            >
              <span style={{ fontSize: "16px" }}>📱</span>
              Install App
            </motion.button>

            <button
              onClick={dismissPrompt}
              style={{
                position: "absolute",
                top: "-8px",
                right: "-8px",
                backgroundColor: "#ef4444",
                color: "white",
                border: "none",
                borderRadius: "50%",
                width: "24px",
                height: "24px",
                fontSize: "12px",
                cursor: "pointer",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                lineHeight: "1",
              }}
            >
              ×
            </button>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default PWAInstallButton;
