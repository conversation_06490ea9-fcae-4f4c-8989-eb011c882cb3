import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import styled from "styled-components";
import { motion } from "framer-motion";
import toast from "../utils/toast";
import {
  Calendar,
  Clock,
  MapPin,
  ArrowRight,
  Plus,
  Users,
  Heart,
  Star,
  Sparkles,
  ChevronRight,
} from "lucide-react";
import { useEventsStore } from "../store";
import { useAuth } from "../hooks/useAuth";
import EventCard from "../components/Events/EventCard";
import EventModal from "../components/Events/EventModal";
import EventForm from "../components/Events/EventForm";
import PWAWelcomeBanner from "../components/PWAWelcomeBanner";
import { formatDate, isToday, isFuture } from "../utils/dateUtils";

const HomeContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing[4]} ${({ theme }) => theme.spacing[4]};
  position: relative;

  /* Subtle background pattern */
  &::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle at 25% 25%,
        ${({ theme }) => theme.colors.primary[50]}40 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 75% 75%,
        ${({ theme }) => theme.colors.secondary[50]}30 0%,
        transparent 50%
      );
    pointer-events: none;
    z-index: -1;
  }

  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing[2]};
  }
`;

const HeroSection = styled.section`
  position: relative;
  text-align: center;
  margin-bottom: ${({ theme }) => theme.spacing[16]};
  padding: ${({ theme }) => theme.spacing[16]}
    ${({ theme }) => theme.spacing[8]};
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors.primary[600]} 0%,
    ${({ theme }) => theme.colors.primary[800]} 50%,
    ${({ theme }) => theme.colors.secondary[600]} 100%
  );
  border-radius: ${({ theme }) => theme.borderRadius["3xl"]};
  overflow: hidden;
  box-shadow: ${({ theme }) => theme.shadows["2xl"]};

  /* Enhanced spiritual pattern overlay */
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle at 20% 20%,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 80%,
        rgba(255, 255, 255, 0.08) 0%,
        transparent 50%
      ),
      url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.08'%3E%3Cpath d='M40 20l4 12h12l-10 7.5 4 12L40 44l-10 7.5 4-12L24 32h12z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    animation: float 20s ease-in-out infinite;
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-10px) rotate(180deg);
    }
  }

  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing[12]}
      ${({ theme }) => theme.spacing[4]};
    margin-bottom: ${({ theme }) => theme.spacing[12]};
  }
`;

const HeroContent = styled.div`
  position: relative;
  z-index: 2;
`;

const HeroIcon = styled(motion.div)`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);

  svg {
    width: 40px;
    height: 40px;
    color: white;
  }

  @media (max-width: 768px) {
    width: 60px;
    height: 60px;

    svg {
      width: 30px;
      height: 30px;
    }
  }
`;

const HeroTitle = styled(motion.h1)`
  font-size: ${({ theme }) => theme.fontSizes["6xl"]};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: white;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  line-height: 1.1;

  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    font-size: ${({ theme }) => theme.fontSizes["5xl"]};
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    font-size: ${({ theme }) => theme.fontSizes["4xl"]};
  }
`;

const HeroSubtitle = styled(motion.p)`
  font-size: ${({ theme }) => theme.fontSizes["xl"]};
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: ${({ theme }) => theme.spacing[8]};
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    font-size: ${({ theme }) => theme.fontSizes.lg};
    max-width: 90%;
  }
`;

const HeroActions = styled(motion.div)`
  display: flex;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing[4]};
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    gap: ${({ theme }) => theme.spacing[3]};
  }
`;

const HeroButton = styled(Link)`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[4]} ${({ theme }) => theme.spacing[8]};
  background: ${({ variant }) =>
    variant === "primary"
      ? "rgba(255, 255, 255, 0.95)"
      : "rgba(255, 255, 255, 0.1)"};
  color: ${({ theme, variant }) =>
    variant === "primary" ? theme.colors.primary[700] : "white"};
  border: ${({ variant }) =>
    variant === "primary" ? "none" : "1px solid rgba(255, 255, 255, 0.2)"};
  border-radius: ${({ theme }) => theme.borderRadius["2xl"]};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  font-size: ${({ theme }) => theme.fontSizes.lg};
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow: ${({ variant }) =>
    variant === "primary"
      ? "0 8px 32px rgba(0, 0, 0, 0.12)"
      : "0 4px 16px rgba(0, 0, 0, 0.1)"};
  min-width: 180px;

  &:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: ${({ variant }) =>
      variant === "primary"
        ? "0 12px 40px rgba(0, 0, 0, 0.15)"
        : "0 8px 24px rgba(0, 0, 0, 0.15)"};
    background: ${({ variant }) =>
      variant === "primary"
        ? "rgba(255, 255, 255, 1)"
        : "rgba(255, 255, 255, 0.15)"};
    text-decoration: none;
  }

  &:active {
    transform: translateY(-1px) scale(1.01);
  }

  @media (max-width: 768px) {
    width: 100%;
    max-width: 280px;
    padding: ${({ theme }) => theme.spacing[4]}
      ${({ theme }) => theme.spacing[6]};
  }
`;

const Section = styled.section`
  margin-bottom: ${({ theme }) => theme.spacing[16]};

  @media (max-width: 768px) {
    margin-bottom: ${({ theme }) => theme.spacing[12]};
  }
`;

const StatsSection = styled(motion.section)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing[6]};
  margin-bottom: ${({ theme }) => theme.spacing[16]};

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: ${({ theme }) => theme.spacing[4]};
    margin-bottom: ${({ theme }) => theme.spacing[12]};
  }
`;

const StatCard = styled(motion.div)`
  background: ${({ theme }) => theme.colors.background.primary};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  border-radius: ${({ theme }) => theme.borderRadius["2xl"]};
  padding: ${({ theme }) => theme.spacing[6]};
  text-align: center;
  box-shadow: ${({ theme }) => theme.shadows.sm};
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-4px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
    border-color: ${({ theme }) => theme.colors.primary[200]};
  }

  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing[4]};
  }
`;

const StatIcon = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: ${({ theme, color }) => theme.colors[color][100]};
  color: ${({ theme, color }) => theme.colors[color][600]};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  margin-bottom: ${({ theme }) => theme.spacing[3]};

  @media (max-width: 768px) {
    width: 40px;
    height: 40px;

    svg {
      width: 20px;
      height: 20px;
    }
  }
`;

const StatNumber = styled.div`
  font-size: ${({ theme }) => theme.fontSizes["3xl"]};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing[1]};

  @media (max-width: 768px) {
    font-size: ${({ theme }) => theme.fontSizes["2xl"]};
  }
`;

const StatLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.secondary};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
`;

const QuickActionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: ${({ theme }) => theme.spacing[3]};
  }
`;

const QuickActionCard = styled(motion.div)`
  background: ${({ theme }) => theme.colors.background.primary};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  border-radius: ${({ theme }) => theme.borderRadius["2xl"]};
  padding: ${({ theme }) => theme.spacing[6]};
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: ${({ color, theme }) => theme.colors[color][500]};
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
    border-color: ${({ color, theme }) => theme.colors[color][200]};
  }
`;

const QuickActionIcon = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background: ${({ color, theme }) => theme.colors[color][100]};
  color: ${({ color, theme }) => theme.colors[color][600]};
  border-radius: ${({ theme }) => theme.borderRadius["2xl"]};
  margin-bottom: ${({ theme }) => theme.spacing[4]};

  svg {
    width: 32px;
    height: 32px;
  }
`;

const QuickActionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0 0 ${({ theme }) => theme.spacing[2]} 0;
`;

const QuickActionDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.text.secondary};
  margin: 0;
  line-height: ${({ theme }) => theme.lineHeights.relaxed};
`;

const SectionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: ${({ theme }) => theme.spacing[8]};
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing[4]};

  @media (max-width: 768px) {
    align-items: flex-start;
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing[3]};
  }
`;

const SectionTitleGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const SectionTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes["4xl"]};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: ${({ theme }) => theme.fontSizes["3xl"]};
  }
`;

const SectionSubtitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  color: ${({ theme }) => theme.colors.text.secondary};
  margin: 0;

  @media (max-width: 768px) {
    font-size: ${({ theme }) => theme.fontSizes.base};
  }
`;

const ViewAllLink = styled(Link)`
  display: inline-flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[5]};
  background: ${({ theme }) => theme.colors.primary[50]};
  color: ${({ theme }) => theme.colors.primary[700]};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  text-decoration: none;
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid ${({ theme }) => theme.colors.primary[200]};

  &:hover {
    background: ${({ theme }) => theme.colors.primary[100]};
    color: ${({ theme }) => theme.colors.primary[800]};
    transform: translateY(-1px);
    box-shadow: ${({ theme }) => theme.shadows.md};
    text-decoration: none;
  }

  @media (max-width: 768px) {
    align-self: flex-start;
  }
`;

const EventsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: ${({ theme }) => theme.spacing[8]};

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: ${({ theme }) => theme.spacing[6]};
  }
`;

const EmptyState = styled(motion.div)`
  text-align: center;
  padding: ${({ theme }) => theme.spacing[16]}
    ${({ theme }) => theme.spacing[4]};
  background: ${({ theme }) => theme.colors.background.secondary};
  border-radius: ${({ theme }) => theme.borderRadius["2xl"]};
  border: 2px dashed ${({ theme }) => theme.colors.border.light};

  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing[12]}
      ${({ theme }) => theme.spacing[4]};
  }
`;

const EmptyStateIcon = styled.div`
  font-size: 4rem;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  opacity: 0.6;
`;

const EmptyStateTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes["2xl"]};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`;

const EmptyStateText = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  color: ${({ theme }) => theme.colors.text.secondary};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
`;

const CreateEventButton = styled.button`
  position: fixed;
  bottom: ${({ theme }) => theme.spacing[6]};
  right: ${({ theme }) => theme.spacing[6]};
  width: 56px;
  height: 56px;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  background: ${({ theme }) => theme.colors.primary[600]};
  color: white;
  box-shadow: ${({ theme }) => theme.shadows.lg};
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all ${({ theme }) => theme.transitions.fast};
  z-index: ${({ theme }) => theme.zIndex[30]};

  &:hover {
    transform: scale(1.1);
    background: ${({ theme }) => theme.colors.primary[700]};
    box-shadow: ${({ theme }) => theme.shadows.xl};
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    bottom: ${({ theme }) => theme.spacing[4]};
    right: ${({ theme }) => theme.spacing[4]};
  }
`;

const HomePage = () => {
  const {
    events,
    isLoading,
    fetchEvents,
    createEvent,
    updateEvent,
    deleteEvent,
  } = useEventsStore();
  const { isAuthenticated, hasPermission } = useAuth();
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [showEventModal, setShowEventModal] = useState(false);
  const [showEventForm, setShowEventForm] = useState(false);
  const [editingEvent, setEditingEvent] = useState(null);

  useEffect(() => {
    fetchEvents({ limit: 6, status: "published" });
  }, [fetchEvents]);

  const upcomingEvents = events.filter((event) =>
    isFuture(new Date(event.startDate))
  );
  const todayEvents = events.filter((event) =>
    isToday(new Date(event.startDate))
  );

  const handleEventClick = (event) => {
    setSelectedEvent(event);
    setShowEventModal(true);
  };

  const handleCreateEvent = () => {
    setEditingEvent(null);
    setShowEventForm(true);
  };

  const handleEditEvent = (event) => {
    setEditingEvent(event);
    setShowEventForm(true);
    setShowEventModal(false);
  };

  const handleDeleteEvent = async (event) => {
    if (window.confirm("Are you sure you want to delete this event?")) {
      const loadingToast = toast.loading("Deleting event...");
      try {
        await deleteEvent(event._id);

        // Refresh the events list to remove the deleted event
        await fetchEvents({ limit: 6, status: "published" });

        setShowEventModal(false);
        toast.dismiss(loadingToast);
        toast.success("Event deleted successfully! 🗑️");
      } catch (error) {
        toast.dismiss(loadingToast);
        const errorMessage =
          error.response?.data?.message || "Failed to delete event";
        toast.error(`Failed to delete event: ${errorMessage}`);
        console.error("Failed to delete event:", error);
      }
    }
  };

  const handleFormSubmit = async (formData) => {
    const action = editingEvent ? "Updating" : "Creating";
    const loadingToast = toast.loading(`${action} event...`);

    try {
      if (editingEvent) {
        await updateEvent(editingEvent._id, formData);
        toast.success("Event updated successfully! ✅");
      } else {
        await createEvent(formData);
        toast.success("Event created successfully! 🎉");
      }

      // Note: fetchEvents is not needed here as the store automatically updates
      // the events list when createEvent or updateEvent is called

      setShowEventForm(false);
      setEditingEvent(null);
      toast.dismiss(loadingToast);
    } catch (error) {
      toast.dismiss(loadingToast);
      const errorMessage =
        error.response?.data?.message || "Failed to save event";
      toast.error(`Failed to ${action.toLowerCase()} event: ${errorMessage}`);
      console.error("Failed to save event:", error);
    }
  };

  const canCreateEvents = isAuthenticated && hasPermission("canCreateEvents");

  return (
    <HomeContainer>
      {/* PWA Welcome Banner */}
      <PWAWelcomeBanner />

      <HeroSection>
        <HeroContent>
          <HeroIcon
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, type: "spring", bounce: 0.4 }}
          >
            <Heart />
          </HeroIcon>
          <HeroTitle
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            Welcome to Grace Bible Fellowship
          </HeroTitle>
          <HeroSubtitle
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            "For where two or three gather in my name, there am I with them."
            <br />
            <em style={{ fontSize: "0.9em", opacity: 0.9 }}>- Matthew 18:20</em>
            <br />
            <br />
            <span style={{ fontSize: "1.1em", fontWeight: "500" }}>
              Join our loving church family in worship, fellowship, and
              spiritual growth. Together, we're building a community rooted in
              God's love and grace.
            </span>
          </HeroSubtitle>
          <HeroActions
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <HeroButton to="/calendar" variant="primary">
              <Calendar size={20} />
              View Calendar
            </HeroButton>
            <HeroButton to="/events">
              <Sparkles size={20} />
              All Events
            </HeroButton>
          </HeroActions>
        </HeroContent>
      </HeroSection>

      {/* Daily Inspiration Section */}
      <Section>
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          style={{
            background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
            borderRadius: "20px",
            padding: "3rem 2rem",
            textAlign: "center",
            border: "1px solid rgba(148, 163, 184, 0.1)",
            boxShadow:
              "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
            marginBottom: "3rem",
          }}
        >
          <motion.div
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            style={{
              fontSize: "3rem",
              marginBottom: "1.5rem",
            }}
          >
            ✨
          </motion.div>
          <motion.h3
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            style={{
              fontSize: "1.5rem",
              fontWeight: "600",
              color: "#1e293b",
              marginBottom: "1rem",
            }}
          >
            Today's Inspiration
          </motion.h3>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
            style={{
              fontSize: "1.2rem",
              fontStyle: "italic",
              color: "#475569",
              lineHeight: "1.6",
              maxWidth: "600px",
              margin: "0 auto",
            }}
          >
            "Trust in the Lord with all your heart and lean not on your own
            understanding; in all your ways submit to him, and he will make your
            paths straight."
            <br />
            <span style={{ fontSize: "1rem", opacity: 0.8 }}>
              - Proverbs 3:5-6
            </span>
          </motion.p>
        </motion.div>
      </Section>

      {/* Stats Section */}
      <StatsSection
        initial={{ opacity: 0, y: 40 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.8 }}
      >
        <StatCard
          whileHover={{ scale: 1.02 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <StatIcon color="primary">
            <Calendar size={24} />
          </StatIcon>
          <StatNumber>{events.length}</StatNumber>
          <StatLabel>Total Events</StatLabel>
        </StatCard>
        <StatCard
          whileHover={{ scale: 1.02 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <StatIcon color="success">
            <Users size={24} />
          </StatIcon>
          <StatNumber>{upcomingEvents.length}</StatNumber>
          <StatLabel>Upcoming Events</StatLabel>
        </StatCard>
        <StatCard
          whileHover={{ scale: 1.02 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <StatIcon color="warning">
            <Star size={24} />
          </StatIcon>
          <StatNumber>{todayEvents.length}</StatNumber>
          <StatLabel>Today's Events</StatLabel>
        </StatCard>
        <StatCard
          whileHover={{ scale: 1.02 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <StatIcon color="info">
            <Heart size={24} />
          </StatIcon>
          <StatNumber>∞</StatNumber>
          <StatLabel>Community Love</StatLabel>
        </StatCard>
      </StatsSection>

      {/* Community Highlights */}
      <Section>
        <SectionHeader>
          <SectionTitleGroup>
            <SectionTitle>Community Highlights</SectionTitle>
            <SectionSubtitle>
              See what's happening in our church family
            </SectionSubtitle>
          </SectionTitleGroup>
        </SectionHeader>

        <StatsSection
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <StatCard
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <StatIcon color="primary">
              <Users size={24} />
            </StatIcon>
            <StatNumber>250+</StatNumber>
            <StatLabel>Active Members</StatLabel>
          </StatCard>

          <StatCard
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <StatIcon color="success">
              <Heart size={24} />
            </StatIcon>
            <StatNumber>15</StatNumber>
            <StatLabel>Ministry Teams</StatLabel>
          </StatCard>

          <StatCard
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <StatIcon color="warning">
              <Sparkles size={24} />
            </StatIcon>
            <StatNumber>52</StatNumber>
            <StatLabel>Weeks of Worship</StatLabel>
          </StatCard>

          <StatCard
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <StatIcon color="info">
              <Star size={24} />
            </StatIcon>
            <StatNumber>100%</StatNumber>
            <StatLabel>God's Love</StatLabel>
          </StatCard>
        </StatsSection>
      </Section>

      {/* Quick Actions for Church Members */}
      <Section>
        <SectionHeader>
          <SectionTitleGroup>
            <SectionTitle>Connect & Grow Together</SectionTitle>
            <SectionSubtitle>
              Your gateway to fellowship, worship, and spiritual growth in our
              church family
            </SectionSubtitle>
          </SectionTitleGroup>
        </SectionHeader>

        <QuickActionsGrid>
          <QuickActionCard
            color="primary"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => window.open("/calendar", "_self")}
          >
            <QuickActionIcon color="primary">
              <Calendar />
            </QuickActionIcon>
            <QuickActionTitle>Church Calendar</QuickActionTitle>
            <QuickActionDescription>
              Discover worship services, Bible studies, and fellowship events.
              Never miss a moment of community connection!
            </QuickActionDescription>
          </QuickActionCard>

          <QuickActionCard
            color="success"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => window.open("/events", "_self")}
          >
            <QuickActionIcon color="success">
              <Users />
            </QuickActionIcon>
            <QuickActionTitle>Fellowship & Events</QuickActionTitle>
            <QuickActionDescription>
              Join our vibrant community in worship, Bible studies, and special
              gatherings that strengthen our faith together
            </QuickActionDescription>
          </QuickActionCard>

          <QuickActionCard
            color="warning"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => alert("Prayer requests feature coming soon!")}
          >
            <QuickActionIcon color="warning">
              <Heart />
            </QuickActionIcon>
            <QuickActionTitle>Prayer & Support</QuickActionTitle>
            <QuickActionDescription>
              Share your heart with our prayer team and lift up others in our
              loving church family through prayer
            </QuickActionDescription>
          </QuickActionCard>

          <QuickActionCard
            color="info"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => alert("Ministry signup feature coming soon!")}
          >
            <QuickActionIcon color="info">
              <Star />
            </QuickActionIcon>
            <QuickActionTitle>Serve & Ministry</QuickActionTitle>
            <QuickActionDescription>
              Use your gifts to serve God and others! Discover meaningful ways
              to make a difference in our community
            </QuickActionDescription>
          </QuickActionCard>
        </QuickActionsGrid>
      </Section>

      {/* Welcome Message for New Visitors */}
      <Section>
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          style={{
            background:
              "linear-gradient(135deg, #fef3c7 0%, #fde68a 50%, #f59e0b 100%)",
            borderRadius: "24px",
            padding: "3rem 2rem",
            textAlign: "center",
            border: "2px solid rgba(245, 158, 11, 0.2)",
            boxShadow:
              "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
            marginBottom: "4rem",
            position: "relative",
            overflow: "hidden",
          }}
        >
          {/* Decorative elements */}
          <div
            style={{
              position: "absolute",
              top: "20px",
              left: "20px",
              fontSize: "2rem",
              opacity: 0.3,
            }}
          >
            🕊️
          </div>
          <div
            style={{
              position: "absolute",
              top: "20px",
              right: "20px",
              fontSize: "2rem",
              opacity: 0.3,
            }}
          >
            ✝️
          </div>
          <div
            style={{
              position: "absolute",
              bottom: "20px",
              left: "50%",
              transform: "translateX(-50%)",
              fontSize: "1.5rem",
              opacity: 0.3,
            }}
          >
            🙏
          </div>

          <motion.div
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            style={{
              fontSize: "3.5rem",
              marginBottom: "1.5rem",
            }}
          >
            🤗
          </motion.div>
          <motion.h3
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            style={{
              fontSize: "2rem",
              fontWeight: "700",
              color: "#92400e",
              marginBottom: "1rem",
            }}
          >
            New Here? You're Welcome!
          </motion.h3>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
            style={{
              fontSize: "1.3rem",
              color: "#a16207",
              lineHeight: "1.6",
              maxWidth: "700px",
              margin: "0 auto 2rem",
              fontWeight: "500",
            }}
          >
            Whether you're exploring faith for the first time or looking for a
            new church home, we'd love to meet you! Come as you are - God loves
            you, and so do we.
          </motion.p>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1 }}
            style={{
              display: "flex",
              gap: "1rem",
              justifyContent: "center",
              flexWrap: "wrap",
            }}
          >
            <button
              style={{
                background: "#92400e",
                color: "white",
                border: "none",
                borderRadius: "12px",
                padding: "12px 24px",
                fontSize: "1.1rem",
                fontWeight: "600",
                cursor: "pointer",
                transition: "all 0.3s ease",
                boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
              }}
              onMouseOver={(e) =>
                (e.target.style.transform = "translateY(-2px)")
              }
              onMouseOut={(e) => (e.target.style.transform = "translateY(0)")}
              onClick={() => alert("Contact information feature coming soon!")}
            >
              📞 Get in Touch
            </button>
            <button
              style={{
                background: "white",
                color: "#92400e",
                border: "2px solid #92400e",
                borderRadius: "12px",
                padding: "12px 24px",
                fontSize: "1.1rem",
                fontWeight: "600",
                cursor: "pointer",
                transition: "all 0.3s ease",
              }}
              onMouseOver={(e) => {
                e.target.style.background = "#92400e";
                e.target.style.color = "white";
                e.target.style.transform = "translateY(-2px)";
              }}
              onMouseOut={(e) => {
                e.target.style.background = "white";
                e.target.style.color = "#92400e";
                e.target.style.transform = "translateY(0)";
              }}
              onClick={() => window.open("/events", "_self")}
            >
              🎉 Explore Events
            </button>
          </motion.div>
        </motion.div>
      </Section>

      {todayEvents.length > 0 && (
        <Section>
          <SectionHeader>
            <SectionTitleGroup>
              <SectionTitle>Today's Events</SectionTitle>
              <SectionSubtitle>
                Don't miss what's happening today in our community
              </SectionSubtitle>
            </SectionTitleGroup>
          </SectionHeader>
          <EventsGrid>
            {todayEvents.map((event, index) => (
              <motion.div
                key={event._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <EventCard
                  event={event}
                  onClick={handleEventClick}
                  onEdit={handleEditEvent}
                  onDelete={handleDeleteEvent}
                />
              </motion.div>
            ))}
          </EventsGrid>
        </Section>
      )}

      <Section>
        <SectionHeader>
          <SectionTitleGroup>
            <SectionTitle>Upcoming Events</SectionTitle>
            <SectionSubtitle>
              Discover what's coming up in our church community
            </SectionSubtitle>
          </SectionTitleGroup>
          <ViewAllLink to="/events">
            View All Events
            <ChevronRight size={18} />
          </ViewAllLink>
        </SectionHeader>

        {isLoading ? (
          <EmptyState
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <EmptyStateIcon>⏳</EmptyStateIcon>
            <EmptyStateTitle>Loading events...</EmptyStateTitle>
            <EmptyStateText>
              Please wait while we fetch the latest events
            </EmptyStateText>
          </EmptyState>
        ) : upcomingEvents.length > 0 ? (
          <EventsGrid>
            {upcomingEvents.slice(0, 6).map((event, index) => (
              <motion.div
                key={event._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <EventCard
                  event={event}
                  onClick={handleEventClick}
                  onEdit={handleEditEvent}
                  onDelete={handleDeleteEvent}
                />
              </motion.div>
            ))}
          </EventsGrid>
        ) : (
          <EmptyState
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <EmptyStateIcon>📅</EmptyStateIcon>
            <EmptyStateTitle>No upcoming events</EmptyStateTitle>
            <EmptyStateText>
              Check back later for new events and announcements. We're always
              planning something special for our community!
            </EmptyStateText>
            {canCreateEvents && (
              <HeroButton
                as="button"
                variant="primary"
                onClick={handleCreateEvent}
                style={{ marginTop: "1rem" }}
              >
                <Plus size={20} />
                Create First Event
              </HeroButton>
            )}
          </EmptyState>
        )}
      </Section>

      {canCreateEvents && (
        <CreateEventButton onClick={handleCreateEvent} title="Create New Event">
          <Plus size={24} />
        </CreateEventButton>
      )}

      <EventModal
        event={selectedEvent}
        isOpen={showEventModal}
        onClose={() => setShowEventModal(false)}
        onEdit={handleEditEvent}
        onDelete={handleDeleteEvent}
      />

      <EventForm
        event={editingEvent}
        selectedDate={null}
        isOpen={showEventForm}
        onClose={() => {
          setShowEventForm(false);
          setEditingEvent(null);
        }}
        onSubmit={handleFormSubmit}
        isLoading={isLoading}
      />
    </HomeContainer>
  );
};

export default HomePage;
