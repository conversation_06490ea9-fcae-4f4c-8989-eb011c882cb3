import { useEffect, useRef, useState } from 'react';

/**
 * Custom hook for handling touch gestures and mobile interactions
 */
export const useTouch = (options = {}) => {
  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    onTap,
    onDoubleTap,
    onLongPress,
    threshold = 50,
    longPressDelay = 500,
    doubleTapDelay = 300
  } = options;

  const touchRef = useRef(null);
  const [touchState, setTouchState] = useState({
    startX: 0,
    startY: 0,
    endX: 0,
    endY: 0,
    startTime: 0,
    lastTap: 0,
    isLongPress: false
  });

  const longPressTimer = useRef(null);
  const doubleTapTimer = useRef(null);

  useEffect(() => {
    const element = touchRef.current;
    if (!element) return;

    const handleTouchStart = (e) => {
      const touch = e.touches[0];
      const now = Date.now();
      
      setTouchState(prev => ({
        ...prev,
        startX: touch.clientX,
        startY: touch.clientY,
        startTime: now,
        isLongPress: false
      }));

      // Start long press timer
      if (onLongPress) {
        longPressTimer.current = setTimeout(() => {
          setTouchState(prev => ({ ...prev, isLongPress: true }));
          onLongPress(e);
        }, longPressDelay);
      }
    };

    const handleTouchMove = (e) => {
      // Cancel long press if user moves finger
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
        longPressTimer.current = null;
      }
    };

    const handleTouchEnd = (e) => {
      const touch = e.changedTouches[0];
      const now = Date.now();
      
      // Clear long press timer
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
        longPressTimer.current = null;
      }

      setTouchState(prev => {
        const newState = {
          ...prev,
          endX: touch.clientX,
          endY: touch.clientY
        };

        // Don't process other gestures if it was a long press
        if (prev.isLongPress) {
          return newState;
        }

        const deltaX = newState.endX - newState.startX;
        const deltaY = newState.endY - newState.startY;
        const deltaTime = now - newState.startTime;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        // Handle swipe gestures
        if (distance > threshold && deltaTime < 500) {
          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            // Horizontal swipe
            if (deltaX > 0 && onSwipeRight) {
              onSwipeRight(e);
            } else if (deltaX < 0 && onSwipeLeft) {
              onSwipeLeft(e);
            }
          } else {
            // Vertical swipe
            if (deltaY > 0 && onSwipeDown) {
              onSwipeDown(e);
            } else if (deltaY < 0 && onSwipeUp) {
              onSwipeUp(e);
            }
          }
        }
        // Handle tap gestures
        else if (distance < threshold && deltaTime < 500) {
          // Check for double tap
          if (onDoubleTap && now - prev.lastTap < doubleTapDelay) {
            if (doubleTapTimer.current) {
              clearTimeout(doubleTapTimer.current);
              doubleTapTimer.current = null;
            }
            onDoubleTap(e);
          } else if (onTap) {
            // Delay single tap to check for double tap
            if (onDoubleTap) {
              doubleTapTimer.current = setTimeout(() => {
                onTap(e);
              }, doubleTapDelay);
            } else {
              onTap(e);
            }
          }
          
          return { ...newState, lastTap: now };
        }

        return newState;
      });
    };

    // Add touch event listeners
    element.addEventListener('touchstart', handleTouchStart, { passive: false });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });
    element.addEventListener('touchend', handleTouchEnd, { passive: false });

    // Cleanup
    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
      
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
      }
      if (doubleTapTimer.current) {
        clearTimeout(doubleTapTimer.current);
      }
    };
  }, [onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown, onTap, onDoubleTap, onLongPress, threshold, longPressDelay, doubleTapDelay]);

  return { touchRef, touchState };
};

/**
 * Hook to detect if the app is running in standalone mode (PWA)
 */
export const useStandalone = () => {
  const [isStandalone, setIsStandalone] = useState(false);

  useEffect(() => {
    const checkStandalone = () => {
      const standalone = window.matchMedia('(display-mode: standalone)').matches ||
                        window.navigator.standalone ||
                        document.referrer.includes('android-app://');
      setIsStandalone(standalone);
    };

    checkStandalone();
    
    // Listen for display mode changes
    const mediaQuery = window.matchMedia('(display-mode: standalone)');
    mediaQuery.addListener(checkStandalone);

    return () => {
      mediaQuery.removeListener(checkStandalone);
    };
  }, []);

  return isStandalone;
};

/**
 * Hook to handle device orientation
 */
export const useOrientation = () => {
  const [orientation, setOrientation] = useState({
    angle: 0,
    type: 'portrait-primary'
  });

  useEffect(() => {
    const handleOrientationChange = () => {
      setOrientation({
        angle: screen.orientation?.angle || window.orientation || 0,
        type: screen.orientation?.type || 'portrait-primary'
      });
    };

    handleOrientationChange();

    // Listen for orientation changes
    if (screen.orientation) {
      screen.orientation.addEventListener('change', handleOrientationChange);
      return () => {
        screen.orientation.removeEventListener('change', handleOrientationChange);
      };
    } else {
      window.addEventListener('orientationchange', handleOrientationChange);
      return () => {
        window.removeEventListener('orientationchange', handleOrientationChange);
      };
    }
  }, []);

  return orientation;
};

/**
 * Hook to detect network status
 */
export const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [connectionType, setConnectionType] = useState('unknown');

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Get connection type if available
    if ('connection' in navigator) {
      const connection = navigator.connection;
      setConnectionType(connection.effectiveType || 'unknown');
      
      const handleConnectionChange = () => {
        setConnectionType(connection.effectiveType || 'unknown');
      };
      
      connection.addEventListener('change', handleConnectionChange);
      
      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
        connection.removeEventListener('change', handleConnectionChange);
      };
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return { isOnline, connectionType };
};
