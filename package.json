{"name": "church-event-management", "version": "1.0.0", "description": "A comprehensive Grace Bible Fellowship system", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"install:all": "npm install && npm install --workspace=backend && npm install --workspace=frontend", "seed:admin": "npm run seed:admin --workspace=backend", "seed:events": "npm run seed:events --workspace=backend", "seed:all": "npm run seed:all --workspace=backend", "dev": "concurrently \"npm run dev --workspace=backend\" \"npm run dev --workspace=frontend\"", "start": "npm run seed:admin && npm run dev", "start:full": "npm run seed:all && npm run dev", "build": "npm run build --workspace=frontend", "backend": "npm run dev --workspace=backend", "frontend": "npm run dev --workspace=frontend", "clean": "rimraf node_modules backend/node_modules frontend/node_modules", "lint": "concurrently \"npm run lint --workspace=backend\" \"npm run lint --workspace=frontend\"", "test": "concurrently \"npm run test --workspace=backend\" \"npm run test --workspace=frontend\""}, "devDependencies": {"concurrently": "^8.2.2", "rimraf": "^5.0.5"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["church", "event-management", "Grace Bible Fellowship"], "author": "Grace Bible Fellowship Team", "license": "MIT"}