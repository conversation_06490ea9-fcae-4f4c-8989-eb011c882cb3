const express = require('express');
const { User, ActivityLog } = require('../models');
const { 
  authenticate, 
  adminOnly, 
  logActivity 
} = require('../middleware/auth');
const { 
  userRegistrationValidation, 
  mongoIdValidation, 
  paginationValidation,
  handleValidationErrors 
} = require('../utils/validation');

const router = express.Router();

// @route   GET /api/users
// @desc    Get all users (Admin only)
// @access  Private (Admin)
router.get('/',
  authenticate,
  adminOnly,
  paginationValidation,
  handleValidationErrors,
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      let query = {};
      
      if (req.query.role) {
        query.role = req.query.role;
      }
      
      if (req.query.isActive !== undefined) {
        query.isActive = req.query.isActive === 'true';
      }

      if (req.query.search) {
        query.$or = [
          { firstName: { $regex: req.query.search, $options: 'i' } },
          { lastName: { $regex: req.query.search, $options: 'i' } },
          { email: { $regex: req.query.search, $options: 'i' } },
          { username: { $regex: req.query.search, $options: 'i' } }
        ];
      }

      const users = await User.find(query)
        .populate('createdBy', 'firstName lastName')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await User.countDocuments(query);

      res.json({
        success: true,
        data: {
          users,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });

    } catch (error) {
      console.error('Get users error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching users'
      });
    }
  }
);

// @route   GET /api/users/:id
// @desc    Get single user (Admin only)
// @access  Private (Admin)
router.get('/:id',
  mongoIdValidation,
  handleValidationErrors,
  authenticate,
  adminOnly,
  async (req, res) => {
    try {
      const user = await User.findById(req.params.id)
        .populate('createdBy', 'firstName lastName');

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      res.json({
        success: true,
        data: {
          user
        }
      });

    } catch (error) {
      console.error('Get user error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching user'
      });
    }
  }
);

// @route   POST /api/users
// @desc    Create new user (Admin only)
// @access  Private (Admin)
router.post('/',
  authenticate,
  adminOnly,
  userRegistrationValidation,
  handleValidationErrors,
  logActivity('create_user', 'user'),
  async (req, res) => {
    try {
      const { username, email, password, firstName, lastName, role, phone } = req.body;

      // Check if user already exists
      const existingUser = await User.findOne({
        $or: [{ email }, { username }]
      });

      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'User with this email or username already exists'
        });
      }

      const userData = {
        username,
        email,
        password,
        firstName,
        lastName,
        role: role || 'editor',
        phone,
        createdBy: req.user._id
      };

      const user = new User(userData);
      await user.save();

      // Remove password from response
      const userResponse = user.toObject();
      delete userResponse.password;

      res.status(201).json({
        success: true,
        message: 'User created successfully',
        data: {
          user: userResponse
        }
      });

    } catch (error) {
      console.error('Create user error:', error);
      
      if (error.name === 'ValidationError') {
        return res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: Object.values(error.errors).map(err => ({
            field: err.path,
            message: err.message
          }))
        });
      }

      if (error.code === 11000) {
        return res.status(400).json({
          success: false,
          message: 'User with this email or username already exists'
        });
      }

      res.status(500).json({
        success: false,
        message: 'Server error while creating user'
      });
    }
  }
);

// @route   PUT /api/users/:id
// @desc    Update user (Admin only)
// @access  Private (Admin)
router.put('/:id',
  mongoIdValidation,
  handleValidationErrors,
  authenticate,
  adminOnly,
  logActivity('update_user', 'user'),
  async (req, res) => {
    try {
      const { firstName, lastName, role, phone, isActive, permissions } = req.body;

      const user = await User.findById(req.params.id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Prevent admin from deactivating themselves
      if (req.params.id === req.user._id.toString() && isActive === false) {
        return res.status(400).json({
          success: false,
          message: 'You cannot deactivate your own account'
        });
      }

      // Update allowed fields
      if (firstName) user.firstName = firstName;
      if (lastName) user.lastName = lastName;
      if (role) user.role = role;
      if (phone !== undefined) user.phone = phone;
      if (isActive !== undefined) user.isActive = isActive;
      if (permissions) {
        user.permissions = { ...user.permissions, ...permissions };
      }

      await user.save();

      res.json({
        success: true,
        message: 'User updated successfully',
        data: {
          user
        }
      });

    } catch (error) {
      console.error('Update user error:', error);
      
      if (error.name === 'ValidationError') {
        return res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: Object.values(error.errors).map(err => ({
            field: err.path,
            message: err.message
          }))
        });
      }

      res.status(500).json({
        success: false,
        message: 'Server error while updating user'
      });
    }
  }
);

// @route   DELETE /api/users/:id
// @desc    Delete user (Admin only)
// @access  Private (Admin)
router.delete('/:id',
  mongoIdValidation,
  handleValidationErrors,
  authenticate,
  adminOnly,
  logActivity('delete_user', 'user'),
  async (req, res) => {
    try {
      const user = await User.findById(req.params.id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Prevent admin from deleting themselves
      if (req.params.id === req.user._id.toString()) {
        return res.status(400).json({
          success: false,
          message: 'You cannot delete your own account'
        });
      }

      await User.findByIdAndDelete(req.params.id);

      res.json({
        success: true,
        message: 'User deleted successfully'
      });

    } catch (error) {
      console.error('Delete user error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while deleting user'
      });
    }
  }
);

module.exports = router;
